from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Body, BackgroundTasks
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import logging
import os
import sys
import json
from pathlib import Path
import chromadb
import ollama
from typing import Optional, List, Dict, Any
import asyncio
from datetime import datetime

from code_preprocessor import MultiLanguageCodeProcessor
from vector_db_creator import VectorDBCreator

# Enhanced logging configuration
logging.basicConfig(
    level=logging.DEBUG if os.getenv("DEBUG", "false").lower() in ["true", "1", "yes"] else logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.StreamHandler(sys.stderr)
    ]
)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="OpenWebUI Multi-Language RAG Tool Server",
    description="Enhanced tool server for C/C++/C#/Python code analysis with multi-codebase support.",
    version="2.1.0",
)

# Add CORS middleware for web interfaces
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration
OLLAMA_HOST = os.getenv("OLLAMA_HOST", "http://localhost:11434")
CHROMA_DB_BASE_PATH = os.getenv("CHROMA_DB_BASE_PATH", "./chroma_db")
SOURCE_CODE_BASE_PATH = os.getenv("SOURCE_CODE_BASE_PATH", "./source_code")
REQUEST_TIMEOUT = 30

# Debug configuration - Force enable for debugging
DEBUG = os.getenv("DEBUG", "false").lower() in ["true", "1", "yes"]

def debug_print(message: str):
    """Enhanced debug printing with multiple outputs"""
    if DEBUG:
        # Force output to both stdout and stderr
        print(f"🔥 [DEBUG] {message}", file=sys.stdout, flush=True)
        print(f"🔥 [DEBUG] {message}", file=sys.stderr, flush=True)
        # Also use logger
        logger.debug(f"🔥 [DEBUG] {message}")

# Enhanced startup debug info
print("=" * 80, flush=True)
print("🚀 STARTING MULTI-LANGUAGE RAG SERVER", flush=True)
print("=" * 80, flush=True)
print(f"🔥 DEBUG MODE: {DEBUG}", flush=True)
print(f"🔥 Python version: {sys.version}", flush=True)
print(f"🔥 Current working directory: {Path.cwd()}", flush=True)
print(f"🔥 CHROMA_DB_BASE_PATH: {CHROMA_DB_BASE_PATH}", flush=True)
print(f"🔥 SOURCE_CODE_BASE_PATH: {SOURCE_CODE_BASE_PATH}", flush=True)
print(f"🔥 Supported Languages: C, C++, Python, C#", flush=True)

# Check if paths exist at startup
source_path = Path(SOURCE_CODE_BASE_PATH)
print(f"🔥 Source path exists: {source_path.exists()}", flush=True)
print(f"🔥 Source path absolute: {source_path.absolute()}", flush=True)

if source_path.exists():
    try:
        items = list(source_path.iterdir())
        print(f"🔥 Source directory contains {len(items)} items:", flush=True)
        for item in items[:10]:  # Show first 10 items
            print(f"🔥   - {item.name} ({'dir' if item.is_dir() else 'file'})", flush=True)
        if len(items) > 10:
            print(f"🔥   ... and {len(items) - 10} more items", flush=True)
    except Exception as e:
        print(f"❌ Error listing source directory: {e}", flush=True)

print("=" * 80, flush=True)

# Global state for managing multiple codebases
active_codebases: Dict[str, Dict] = {}
current_codebase: Optional[str] = None

# --- Enhanced RAG Service with Multi-Language Support ---

class MultiLanguageRAGService:
    def __init__(self):
        print("🚀 [INIT] Initializing MultiLanguageRAGService...", flush=True)
        logger.info("Initializing MultiLanguageRAGService")
        
        try:
            self.chroma_client = chromadb.PersistentClient(path=CHROMA_DB_BASE_PATH)
            print("✅ [INIT] ChromaDB client initialized successfully", flush=True)
        except Exception as e:
            print(f"❌ [INIT] ChromaDB initialization failed: {e}", flush=True)
            raise
        
        self.active_collections: Dict[str, Any] = {}
        self.preprocessor = MultiLanguageCodeProcessor("")  # Will be updated per codebase
        self.db_creator = VectorDBCreator(CHROMA_DB_BASE_PATH)

        print("✅ [INIT] Multi-Language RAG Service initialized successfully", flush=True)
        logger.info(f"Initialized Multi-Language RAG Service at {CHROMA_DB_BASE_PATH}")
    
    def list_available_codebases(self) -> List[Dict]:
        """List all available codebases with enhanced debugging"""
        print("=" * 60, flush=True)
        print("🔍 [LIST] Starting codebase discovery...", flush=True)
        print(f"🔍 [LIST] SOURCE_CODE_BASE_PATH: {SOURCE_CODE_BASE_PATH}", flush=True)
        
        source_path = Path(SOURCE_CODE_BASE_PATH)
        abs_path = source_path.absolute()
        print(f"🔍 [LIST] Absolute path: {abs_path}", flush=True)
        print(f"🔍 [LIST] Path exists: {source_path.exists()}", flush=True)
        print(f"🔍 [LIST] Path is directory: {source_path.is_dir() if source_path.exists() else 'N/A'}", flush=True)

        codebases = []

        # Scan source code directory for subdirectories
        if source_path.exists():
            print("🔍 [LIST] Source directory found, scanning contents...", flush=True)
            try:
                items = list(source_path.iterdir())
                print(f"🔍 [LIST] Found {len(items)} items in source directory", flush=True)

                for item in items:
                    item_name = item.name
                    is_dir = item.is_dir()
                    starts_with_dot = item_name.startswith('.')
                    
                    print(f"🔍 [LIST] Examining: '{item_name}' (is_dir: {is_dir}, hidden: {starts_with_dot})", flush=True)

                    if is_dir and not starts_with_dot:
                        print(f"🔍 [LIST] Processing codebase directory: {item_name}", flush=True)
                        try:
                            codebase_info = self._get_codebase_info(item_name)
                            print(f"🔍 [LIST] Codebase info for {item_name}: {codebase_info}", flush=True)
                            codebases.append(codebase_info)
                        except Exception as e:
                            print(f"❌ [LIST] Error getting info for {item_name}: {e}", flush=True)
                    else:
                        reason = "not a directory" if not is_dir else "hidden (starts with dot)"
                        print(f"🔍 [LIST] Skipping '{item_name}': {reason}", flush=True)

            except Exception as e:
                print(f"❌ [LIST] Error scanning source directory: {e}", flush=True)
                logger.error(f"Error scanning source directory: {e}")
        else:
            print(f"❌ [LIST] Source code directory does not exist: {source_path}", flush=True)
            print(f"🔍 [LIST] Current working directory contents:", flush=True)
            try:
                cwd_items = list(Path.cwd().iterdir())
                for item in cwd_items[:10]:
                    print(f"🔍 [LIST]   - {item.name} ({'dir' if item.is_dir() else 'file'})", flush=True)
                if len(cwd_items) > 10:
                    print(f"🔍 [LIST]   ... and {len(cwd_items) - 10} more items", flush=True)
            except Exception as e:
                print(f"❌ [LIST] Error listing current directory: {e}", flush=True)

        # Also check for existing ChromaDB collections
        print(f"🔍 [LIST] Checking ChromaDB collections...", flush=True)
        try:
            collections = self.chroma_client.list_collections()
            print(f"🔍 [LIST] Found {len(collections)} ChromaDB collections", flush=True)

            for collection in collections:
                codebase_name = collection.name
                print(f"🔍 [LIST] Found collection: {codebase_name}", flush=True)

                # Check if already in codebases list
                existing_names = [c['name'] for c in codebases]
                if codebase_name not in existing_names:
                    print(f"🔍 [LIST] Adding collection-only codebase: {codebase_name}", flush=True)
                    try:
                        doc_count = collection.count()
                        codebases.append({
                            'name': codebase_name,
                            'status': 'indexed_only',
                            'has_source': False,
                            'has_database': True,
                            'document_count': doc_count,
                            'last_updated': 'unknown'
                        })
                        print(f"🔍 [LIST] Added collection-only codebase with {doc_count} documents", flush=True)
                    except Exception as e:
                        print(f"❌ [LIST] Error getting collection info for {codebase_name}: {e}", flush=True)
                else:
                    print(f"🔍 [LIST] Collection {codebase_name} already in codebases list", flush=True)

        except Exception as e:
            print(f"❌ [LIST] Error listing ChromaDB collections: {e}", flush=True)
            logger.error(f"Error listing collections: {e}")

        print(f"🔍 [LIST] Final result: {len(codebases)} codebases found", flush=True)
        for i, cb in enumerate(codebases):
            print(f"🔍 [LIST]   {i+1}. {cb['name']}: {cb['status']} (docs: {cb['document_count']})", flush=True)

        print("=" * 60, flush=True)
        return codebases
    
    def _get_codebase_info(self, codebase_name: str) -> Dict:
        """Get information about a specific codebase with language detection"""
        print(f"🔍 [INFO] Getting info for codebase: {codebase_name}", flush=True)

        source_path = Path(SOURCE_CODE_BASE_PATH) / codebase_name
        print(f"🔍 [INFO] Source path: {source_path}", flush=True)
        print(f"🔍 [INFO] Source path exists: {source_path.exists()}", flush=True)
        
        has_source = source_path.exists() and source_path.is_dir()
        print(f"🔍 [INFO] Has source: {has_source}", flush=True)

        # Language detection if source exists
        detected_languages = set()
        file_counts = {}
        
        if has_source:
            try:
                # Scan for language files
                source_extensions = {
                    '.c': 'C', '.h': 'C/C++', '.cpp': 'C++', '.cxx': 'C++', 
                    '.cc': 'C++', '.c++': 'C++', '.hpp': 'C++', '.hxx': 'C++', 
                    '.hh': 'C++', '.py': 'Python', '.pyw': 'Python', '.cs': 'C#'
                }
                
                for root, dirs, files in os.walk(source_path):
                    for file in files:
                        ext = Path(file).suffix.lower()
                        if ext in source_extensions:
                            lang = source_extensions[ext]
                            detected_languages.add(lang)
                            file_counts[ext] = file_counts.get(ext, 0) + 1
                
                print(f"🔍 [INFO] Detected languages in {codebase_name}: {list(detected_languages)}", flush=True)
                print(f"🔍 [INFO] File counts: {file_counts}", flush=True)
                
            except Exception as e:
                print(f"❌ [INFO] Error scanning files in {codebase_name}: {e}", flush=True)

        # Check if collection exists
        has_database = False
        document_count = 0
        last_updated = 'never'

        print(f"🔍 [INFO] Checking ChromaDB collection for {codebase_name}", flush=True)
        try:
            collection = self.chroma_client.get_collection(codebase_name)
            has_database = True
            document_count = collection.count()
            print(f"🔍 [INFO] Collection {codebase_name} found with {document_count} documents", flush=True)

            # Try to get last updated from metadata
            if document_count > 0:
                try:
                    sample = collection.get(limit=1, include=['metadatas'])
                    if sample['metadatas'] and len(sample['metadatas']) > 0:
                        last_updated = sample['metadatas'][0].get('processed_date', 'unknown')
                        print(f"🔍 [INFO] Last updated: {last_updated}", flush=True)
                except Exception as e:
                    print(f"⚠️ [INFO] Could not get metadata sample: {e}", flush=True)

        except Exception as e:
            has_database = False
            print(f"🔍 [INFO] No collection found for {codebase_name}: {str(e)[:100]}", flush=True)

        # Determine status
        if has_source and has_database:
            status = 'ready'
        elif has_source and not has_database:
            status = 'needs_indexing'
        elif not has_source and has_database:
            status = 'indexed_only'
        else:
            status = 'unknown'

        print(f"🔍 [INFO] Final status for {codebase_name}: {status}", flush=True)

        result = {
            'name': codebase_name,
            'status': status,
            'has_source': has_source,
            'has_database': has_database,
            'document_count': document_count,
            'last_updated': last_updated,
            'source_path': str(source_path) if has_source else None,
            'detected_languages': list(detected_languages),
            'file_counts': file_counts
        }

        print(f"🔍 [INFO] Returning codebase info: {result}", flush=True)
        return result
    
    def select_codebase(self, codebase_name: str) -> Dict:
        """Select and load a specific codebase"""
        print(f"🎯 [SELECT] Selecting codebase: {codebase_name}", flush=True)
        try:
            collection = self.chroma_client.get_collection(codebase_name)
            self.active_collections[codebase_name] = collection
            
            codebase_info = self._get_codebase_info(codebase_name)
            print(f"✅ [SELECT] Successfully selected {codebase_name} with {collection.count()} documents", flush=True)
            logger.info(f"Selected codebase: {codebase_name} with {collection.count()} documents")
            
            return {
                'success': True,
                'codebase': codebase_name,
                'info': codebase_info
            }
        
        except Exception as e:
            print(f"❌ [SELECT] Error selecting codebase {codebase_name}: {e}", flush=True)
            logger.error(f"Error selecting codebase {codebase_name}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_active_collection(self, codebase_name: str):
        """Get the active collection for a codebase"""
        if codebase_name not in self.active_collections:
            result = self.select_codebase(codebase_name)
            if not result['success']:
                raise Exception(f"Cannot access codebase: {result['error']}")
        
        return self.active_collections[codebase_name]
    
    async def process_codebase(self, codebase_name: str, exclude_dirs: Optional[List[str]] = None) -> Dict:
        """Process a codebase: preprocess code and create vector database"""
        print(f"⚙️ [PROCESS] Starting processing of codebase: {codebase_name}", flush=True)
        source_path = Path(SOURCE_CODE_BASE_PATH) / codebase_name
        
        if not source_path.exists():
            raise Exception(f"Source code directory not found: {source_path}")
        
        try:
            # Step 1: Preprocess the code
            print(f"⚙️ [PROCESS] Starting preprocessing of codebase: {codebase_name}", flush=True)
            logger.info(f"Starting preprocessing of codebase: {codebase_name}")
            self.preprocessor = MultiLanguageCodeProcessor(str(source_path))
            
            exclude_set = set(exclude_dirs) if exclude_dirs else {
                '.git', '.svn', 'build', 'dist', '__pycache__', 
                'node_modules', 'Debug', 'Release', '.vscode', '.idea',
                'bin', 'obj', '.vs', 'packages'  # Added C# specific excludes
            }
            
            chunks = self.preprocessor.process_repository(exclude_dirs=exclude_set)
            
            if not chunks:
                raise Exception("No code chunks were generated during preprocessing")
            
            # Add processing metadata to chunks
            current_time = datetime.now().isoformat()
            for chunk in chunks:
                chunk['metadata']['codebase_name'] = codebase_name
                chunk['metadata']['processed_date'] = current_time
            
            print(f"⚙️ [PROCESS] Preprocessing complete: {len(chunks)} chunks generated", flush=True)
            logger.info(f"Preprocessing complete: {len(chunks)} chunks generated")
            
            # Step 2: Create vector database
            print(f"⚙️ [PROCESS] Creating vector database for codebase: {codebase_name}", flush=True)
            logger.info(f"Creating vector database for codebase: {codebase_name}")
            
            # Use the existing VectorDBCreator but update collection name
            self.db_creator.collection_name = codebase_name
            collection = self.db_creator.create_collection(chunks)
            
            # Update active collections
            self.active_collections[codebase_name] = collection
            
            print(f"✅ [PROCESS] Vector database creation complete: {collection.count()} documents indexed", flush=True)
            logger.info(f"Vector database creation complete: {collection.count()} documents indexed")
            
            return {
                'success': True,
                'codebase': codebase_name,
                'chunks_processed': len(chunks),
                'documents_indexed': collection.count(),
                'processing_time': current_time
            }
        
        except Exception as e:
            print(f"❌ [PROCESS] Error processing codebase {codebase_name}: {e}", flush=True)
            logger.error(f"Error processing codebase {codebase_name}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def search(self, query: str, codebase_name: str, n_results: int = 5, 
               use_ollama: bool = False, **filters) -> List[Dict]:
        """Search within a specific codebase"""
        print(f"🔍 [SEARCH] Searching {codebase_name} for: {query}", flush=True)
        collection = self.get_active_collection(codebase_name)
        
        # Build filters
        chroma_filters = self._build_filters(**filters)
        
        try:
            if use_ollama:
                return self._search_with_ollama_embeddings(
                    collection, query, n_results, chroma_filters
                )
            else:
                return self._search_with_chromadb_embeddings(
                    collection, query, n_results, chroma_filters
                )
        except Exception as e:
            print(f"❌ [SEARCH] Search error in {codebase_name}: {e}", flush=True)
            logger.error(f"Search error in {codebase_name}: {e}")
            raise
    
    def _build_filters(self, filter_type: Optional[str] = None, 
                      filter_language: Optional[str] = None,
                      filter_file: Optional[str] = None) -> Optional[Dict]:
        """Build ChromaDB filters based on parameters"""
        filters = {}
        
        if filter_type:
            filters["type"] = {"$eq": filter_type}
        
        if filter_language:
            filters["language"] = {"$eq": filter_language}
            
        if filter_file:
            filters["relative_path"] = {"$contains": filter_file}
        
        return filters if filters else None
    
    def _search_with_chromadb_embeddings(self, collection, query: str, n_results: int, filters):
        """Search using ChromaDB's built-in embeddings"""
        results = collection.query(
            query_texts=[query],
            n_results=n_results,
            where=filters,
            include=["documents", "metadatas", "distances"]
        )
        
        return self._format_search_results(results)
    
    def _search_with_ollama_embeddings(self, collection, query: str, n_results: int, filters):
        """Search using Ollama embeddings"""
        try:
            # Create client with proper host
            client = ollama.Client(host=OLLAMA_HOST)
            response = client.embeddings(model="nomic-embed-text", prompt=query)
            query_embedding = response["embedding"]
            
            results = collection.query(
                query_embeddings=[query_embedding],
                n_results=n_results,
                where=filters,
                include=["documents", "metadatas", "distances"]
            )
            
            return self._format_search_results(results)
        
        except Exception as e:
            logger.error(f"Ollama search error: {e}")
            # Fallback to ChromaDB embeddings
            return self._search_with_chromadb_embeddings(collection, query, n_results, filters)
    
    def _format_search_results(self, results) -> List[Dict]:
        """Format search results consistently"""
        formatted_results = []
        if results['documents'][0]:
            for i, doc in enumerate(results['documents'][0]):
                formatted_results.append({
                    'content': doc,
                    'metadata': results['metadatas'][0][i],
                    'distance': results['distances'][0][i],
                    'relevance_score': 1.0 - results['distances'][0][i]
                })
        
        return formatted_results
    
    def generate_response(self, query: str, context_chunks: List[Dict], 
                         model: str = "codellama:7b") -> str:
        """Generate response using retrieved context with multi-language awareness"""
        if not context_chunks:
            return "No relevant code chunks found for your query."
        
        # Detect languages in context
        context_languages = set()
        for chunk in context_chunks:
            lang = chunk['metadata'].get('language', 'unknown')
            context_languages.add(lang)
        
        # Format context with enhanced metadata
        context_sections = []
        for i, chunk in enumerate(context_chunks, 1):
            formatted_chunk = self._format_context_chunk(chunk)
            context_sections.append(f"### Context {i}:\n{formatted_chunk}")
        
        context = "\n".join(context_sections)
        
        # Language-aware prompt
        languages_str = ", ".join(sorted(context_languages))
        
        prompt = f"""You are an expert software developer analyzing a multi-language codebase containing {languages_str} code. Answer the user's question based on the provided source code context.

CONTEXT FROM CODEBASE:
{context}

USER QUESTION: {query}

INSTRUCTIONS:
1. Provide a clear, technical answer based on the code context
2. Reference specific functions, classes, methods, or files when relevant
3. Include code snippets if they help explain your answer
4. If the context doesn't fully answer the question, say so explicitly
5. Focus on practical implementation details and relationships between code components
6. Consider language-specific patterns and conventions when relevant
7. If multiple languages are involved, explain how they interact or differ

ANSWER:"""

        try:
            # Create client with proper host
            client = ollama.Client(host=OLLAMA_HOST)
            response = client.generate(
                model=model,
                prompt=prompt,
                options={
                    "temperature": 0.1,
                    "top_p": 0.9,
                    "num_ctx": 4096
                }
            )
            
            return response['response']
            
        except Exception as e:
            logger.error(f"Response generation error: {e}")
            return f"Error generating response: {str(e)}"
    
    def _format_context_chunk(self, chunk: Dict) -> str:
        """Format a chunk for context with enhanced metadata"""
        metadata = chunk['metadata']
        content = chunk['content']
        
        context_parts = []
        
        if 'relative_path' in metadata:
            context_parts.append(f"File: {metadata['relative_path']}")
        
        chunk_type = metadata.get('type', 'unknown')
        language = metadata.get('language', 'unknown')
        context_parts.append(f"Type: {chunk_type} ({language})")
        
        if chunk_type == 'function' and 'function_name' in metadata:
            context_parts.append(f"Function: {metadata['function_name']}")
        elif chunk_type == 'class' and 'class_name' in metadata:
            context_parts.append(f"Class: {metadata['class_name']}")
        elif chunk_type == 'method':
            class_name = metadata.get('class_name', 'Unknown')
            method_name = metadata.get('method_name', 'Unknown')
            context_parts.append(f"Method: {class_name}::{method_name}")
        
        if 'start_line' in metadata and 'end_line' in metadata:
            context_parts.append(f"Lines: {metadata['start_line']}-{metadata['end_line']}")
        
        if 'relevance_score' in chunk:
            context_parts.append(f"Relevance: {chunk['relevance_score']:.3f}")
        
        header = " | ".join(context_parts)
        return f"=== {header} ===\n{content}\n"
    
    def get_codebase_stats(self, codebase_name: str) -> Dict:
        """Get statistics about a specific codebase with language breakdown"""
        print(f"📊 [STATS] Getting stats for codebase: {codebase_name}", flush=True)
        try:
            collection = self.get_active_collection(codebase_name)
            total_docs = collection.count()
            
            # Get sample to analyze types
            sample = collection.get(limit=min(1000, total_docs), include=["metadatas"])
            
            type_counts = {}
            language_counts = {}
            file_counts = set()
            
            for metadata in sample['metadatas']:
                chunk_type = metadata.get('type', 'unknown')
                type_counts[chunk_type] = type_counts.get(chunk_type, 0) + 1
                
                language = metadata.get('language', 'unknown')
                language_counts[language] = language_counts.get(language, 0) + 1
                
                filepath = metadata.get('relative_path', 'unknown')
                file_counts.add(filepath)
            
            result = {
                'codebase_name': codebase_name,
                'total_documents': total_docs,
                'unique_files': len(file_counts),
                'chunk_types': type_counts,
                'languages': language_counts,
                'last_updated': sample['metadatas'][0].get('processed_date', 'unknown') if sample['metadatas'] else 'unknown'
            }
            
            print(f"📊 [STATS] Stats for {codebase_name}: {total_docs} docs, {len(file_counts)} files", flush=True)
            return result
            
        except Exception as e:
            print(f"❌ [STATS] Stats error for {codebase_name}: {e}", flush=True)
            logger.error(f"Stats error for {codebase_name}: {e}")
            return {'error': str(e)}

# Initialize service with enhanced error handling
print("🚀 [STARTUP] Attempting to initialize Multi-Language RAG service...", flush=True)
try:
    rag_service = MultiLanguageRAGService()
    print("✅ [STARTUP] Multi-Language RAG service initialized successfully!", flush=True)
except Exception as e:
    print(f"❌ [STARTUP] Failed to initialize RAG service: {e}", flush=True)
    logger.error(f"Failed to initialize RAG service: {e}")
    import traceback
    print(f"❌ [STARTUP] Full traceback: {traceback.format_exc()}", flush=True)
    rag_service = None

# --- Pydantic Models ---

class CodebaseSelectArgs(BaseModel):
    codebase_name: str = Field(..., description="Name of the codebase to select")

class CodebaseProcessArgs(BaseModel):
    codebase_name: str = Field(..., description="Name of the codebase to process")
    exclude_dirs: Optional[List[str]] = Field(None, description="Directories to exclude during processing")

class CodeSearchArgs(BaseModel):
    query: str = Field(..., description="The search query for code")
    codebase_name: str = Field(..., description="Name of the codebase to search")
    n_results: int = Field(default=5, ge=1, le=10, description="Number of results to return")
    filter_type: Optional[str] = Field(None, description="Filter by type (function, class, method, etc.)")
    filter_language: Optional[str] = Field(None, description="Filter by language (c, cpp, python, csharp)")
    filter_file: Optional[str] = Field(None, description="Filter by file pattern")

class CodeQuestionArgs(BaseModel):
    question: str = Field(..., description="Question about the codebase")
    codebase_name: str = Field(..., description="Name of the codebase to query")
    n_results: int = Field(default=5, ge=1, le=10, description="Number of context chunks to use")
    filter_type: Optional[str] = Field(None, description="Filter by type")
    filter_language: Optional[str] = Field(None, description="Filter by language")
    filter_file: Optional[str] = Field(None, description="Filter by file pattern")

class CodeAnalysisArgs(BaseModel):
    codebase_name: str = Field(..., description="Name of the codebase to analyze")
    analysis_type: str = Field(default="overview", description="Type of analysis")
    file_pattern: Optional[str] = Field(None, description="Analyze specific files matching pattern")
    filter_language: Optional[str] = Field(None, description="Filter by language")

# --- Codebase Management Tools ---

@app.post("/tools/list_codebases")
async def list_codebases():
    """List all available codebases with enhanced debugging"""
    print("=" * 80, flush=True)
    print("📡 [ENDPOINT] /tools/list_codebases called", flush=True)
    print("=" * 80, flush=True)

    if rag_service is None:
        print("❌ [ENDPOINT] RAG service is None!", flush=True)
        raise HTTPException(status_code=503, detail="RAG service not available")

    print("📡 [ENDPOINT] RAG service is available, calling list_available_codebases()", flush=True)

    try:
        codebases = rag_service.list_available_codebases()
        print(f"📡 [ENDPOINT] Service returned {len(codebases)} codebases", flush=True)

        if not codebases:
            print("📡 [ENDPOINT] No codebases found, returning help message", flush=True)
            return JSONResponse(content={"result": "No codebases found. Place source code in subdirectories under ./source_code/ (supports C/C++/Python/C#)"})

        print(f"📡 [ENDPOINT] Building response for {len(codebases)} codebases", flush=True)
        result_parts = ["📚 **Available Codebases (Multi-Language Support):**\n"]

        for i, codebase in enumerate(codebases):
            name = codebase['name']
            status = codebase['status']
            doc_count = codebase['document_count']
            last_updated = codebase['last_updated']
            detected_languages = codebase.get('detected_languages', [])
            file_counts = codebase.get('file_counts', {})

            print(f"📡 [ENDPOINT] Processing codebase {i+1}: {name} (status: {status}, docs: {doc_count})", flush=True)

            status_emoji = {
                'ready': '✅',
                'needs_indexing': '⚠️',
                'indexed_only': '📦',
                'unknown': '❓'
            }.get(status, '❓')

            result_parts.append(f"**{status_emoji} {name}**")
            result_parts.append(f"   Status: {status}")
            result_parts.append(f"   Documents: {doc_count:,}")
            result_parts.append(f"   Last Updated: {last_updated}")
            
            if detected_languages:
                languages_str = ", ".join(detected_languages)
                result_parts.append(f"   Languages: {languages_str}")
            
            if file_counts:
                file_summary = ", ".join([f"{ext}({count})" for ext, count in sorted(file_counts.items())])
                result_parts.append(f"   Files: {file_summary}")
            
            result_parts.append("")

        result_text = "\n".join(result_parts)
        print(f"📡 [ENDPOINT] Response length: {len(result_text)} characters", flush=True)
        print(f"📡 [ENDPOINT] Response preview: {result_text[:200]}...", flush=True)
        
        response = JSONResponse(content={"result": result_text})
        print("📡 [ENDPOINT] Returning successful response", flush=True)
        return response

    except Exception as e:
        print(f"❌ [ENDPOINT] Exception in list_codebases: {e}", flush=True)
        import traceback
        print(f"❌ [ENDPOINT] Full traceback: {traceback.format_exc()}", flush=True)
        logger.error(f"Error listing codebases: {e}")
        return JSONResponse(content={"result": f"❌ Error listing codebases: {str(e)}"})

@app.post("/tools/select_codebase")
async def select_codebase(args: CodebaseSelectArgs = Body(...)):
    """Select a codebase for subsequent operations"""
    print(f"🎯 [ENDPOINT] Selecting codebase: {args.codebase_name}", flush=True)
    
    if rag_service is None:
        raise HTTPException(status_code=503, detail="RAG service not available")
    
    global current_codebase
    
    try:
        result = rag_service.select_codebase(args.codebase_name)
        
        if result['success']:
            current_codebase = args.codebase_name
            info = result['info']
            
            result_text = f"""✅ **Selected Codebase: {args.codebase_name}**

📊 **Statistics:**
- Status: {info['status']}
- Documents: {info['document_count']:,}
- Last Updated: {info['last_updated']}
- Has Source: {'Yes' if info['has_source'] else 'No'}
- Has Database: {'Yes' if info['has_database'] else 'No'}"""

            # Add language information if available
            if info.get('detected_languages'):
                languages_str = ", ".join(info['detected_languages'])
                result_text += f"\n- Languages: {languages_str}"
            
            if info.get('file_counts'):
                file_summary = ", ".join([f"{ext}({count})" for ext, count in sorted(info['file_counts'].items())])
                result_text += f"\n- Files: {file_summary}"

            result_text += "\n\n🔍 You can now search and analyze this codebase using the other tools."
            
            print(f"✅ [ENDPOINT] Successfully selected codebase: {args.codebase_name}", flush=True)
            return JSONResponse(content={"result": result_text})
        else:
            print(f"❌ [ENDPOINT] Failed to select codebase: {result['error']}", flush=True)
            return JSONResponse(content={"result": f"❌ Failed to select codebase: {result['error']}"})
        
    except Exception as e:
        print(f"❌ [ENDPOINT] Exception selecting codebase: {e}", flush=True)
        logger.error(f"Error selecting codebase: {e}")
        return JSONResponse(content={"result": f"❌ Error selecting codebase: {str(e)}"})

@app.post("/tools/process_codebase")
async def process_codebase(args: CodebaseProcessArgs = Body(...), background_tasks: BackgroundTasks = None):
    """Process a codebase: preprocess code and create vector database"""
    print(f"⚙️ [ENDPOINT] Processing codebase: {args.codebase_name}", flush=True)
    
    if rag_service is None:
        raise HTTPException(status_code=503, detail="RAG service not available")
    
    try:
        # Start processing in background
        result = await rag_service.process_codebase(args.codebase_name, args.exclude_dirs)
        
        if result['success']:
            result_text = f"""✅ **Multi-Language Codebase Processing Complete: {args.codebase_name}**

📊 **Processing Results:**
- Chunks Processed: {result['chunks_processed']:,}
- Documents Indexed: {result['documents_indexed']:,}
- Processing Time: {result['processing_time']}

🔍 The codebase is now ready for search and analysis across all supported languages (C/C++/Python/C#)!"""
            
            print(f"✅ [ENDPOINT] Successfully processed codebase: {args.codebase_name}", flush=True)
            return JSONResponse(content={"result": result_text})
        else:
            print(f"❌ [ENDPOINT] Processing failed: {result['error']}", flush=True)
            return JSONResponse(content={"result": f"❌ Processing failed: {result['error']}"})
        
    except Exception as e:
        print(f"❌ [ENDPOINT] Exception processing codebase: {e}", flush=True)
        logger.error(f"Error processing codebase: {e}")
        return JSONResponse(content={"result": f"❌ Error processing codebase: {str(e)}"})

# --- Enhanced Search and Analysis Tools ---

@app.post("/tools/search_code")
async def search_code(args: CodeSearchArgs = Body(...)):
    """Search through a specific codebase for relevant code snippets"""
    print(f"🔍 [ENDPOINT] Searching {args.codebase_name} for: {args.query}", flush=True)
    
    if rag_service is None:
        raise HTTPException(status_code=503, detail="RAG service not available")
    
    logger.info(f"Searching in codebase '{args.codebase_name}' with query: {args.query}")
    
    try:
        results = rag_service.search(
            query=args.query,
            codebase_name=args.codebase_name,
            n_results=args.n_results,
            filter_type=args.filter_type,
            filter_language=args.filter_language,
            filter_file=args.filter_file
        )
        
        if not results:
            filter_info = []
            if args.filter_language:
                filter_info.append(f"language: {args.filter_language}")
            if args.filter_type:
                filter_info.append(f"type: {args.filter_type}")
            if args.filter_file:
                filter_info.append(f"file: {args.filter_file}")
            
            filter_str = f" with filters ({', '.join(filter_info)})" if filter_info else ""
            return JSONResponse(content={"result": f"No relevant code found in '{args.codebase_name}'{filter_str} for your search query."})
        
        # Format results for display
        formatted_results = []
        for i, result in enumerate(results, 1):
            metadata = result.get('metadata', {})
            content = result.get('content', '')
            relevance = result.get('relevance_score', 0)
            
            file_path = metadata.get('relative_path', 'Unknown file')
            chunk_type = metadata.get('type', 'unknown')
            language = metadata.get('language', 'unknown')
            
            # Get function/class name if available
            identifier = ""
            if chunk_type == 'function' and 'function_name' in metadata:
                identifier = f" `{metadata['function_name']}()`"
            elif chunk_type == 'class' and 'class_name' in metadata:
                identifier = f" `{metadata['class_name']}`"
            elif chunk_type == 'method':
                class_name = metadata.get('class_name', 'Unknown')
                method_name = metadata.get('method_name', 'Unknown')
                identifier = f" `{class_name}::{method_name}()`"
            
            # Truncate content if too long
            display_content = content[:400] + "..." if len(content) > 400 else content
            
            formatted_results.append(f"""
**Result {i}** (Relevance: {relevance:.3f})
📁 **File**: `{file_path}` | **Language**: {language.upper()} | **Type**: {chunk_type}{identifier}

```{language}
{display_content}
```
""")
        
        # Add filter information to the header
        filter_info = []
        if args.filter_language:
            filter_info.append(f"Language: {args.filter_language.upper()}")
        if args.filter_type:
            filter_info.append(f"Type: {args.filter_type}")
        if args.filter_file:
            filter_info.append(f"File: {args.filter_file}")
        
        filter_str = f" | Filters: {', '.join(filter_info)}" if filter_info else ""
        
        result_text = f"🔍 **Codebase**: {args.codebase_name}{filter_str}\n🔍 Found {len(results)} relevant code snippets:\n\n" + "\n".join(formatted_results)
        
        print(f"✅ [ENDPOINT] Search complete, found {len(results)} results", flush=True)
        return JSONResponse(content={"result": result_text})
        
    except Exception as e:
        print(f"❌ [ENDPOINT] Search error: {e}", flush=True)
        logger.error(f"Code search error: {e}")
        return JSONResponse(content={"result": f"❌ Error searching code: {str(e)}"})

@app.post("/tools/ask_about_code")
async def ask_about_code(args: CodeQuestionArgs = Body(...)):
    """Ask a question about a specific codebase and get an AI-generated answer"""
    print(f"🤖 [ENDPOINT] Asking about {args.codebase_name}: {args.question}", flush=True)
    
    if rag_service is None:
        raise HTTPException(status_code=503, detail="RAG service not available")
    
    logger.info(f"Asking about codebase '{args.codebase_name}' with question: {args.question}")
    
    try:
        # Get relevant chunks
        chunks = rag_service.search(
            query=args.question,
            codebase_name=args.codebase_name,
            n_results=args.n_results,
            filter_type=args.filter_type,
            filter_language=args.filter_language,
            filter_file=args.filter_file
        )
        
        # Generate response
        response = rag_service.generate_response(args.question, chunks)
        sources = [chunk['metadata'] for chunk in chunks]
        
        # Format response with sources
        result_parts = [f"🤖 **AI Analysis of {args.codebase_name}:** {response}"]
        
        if sources:
            # Group sources by language for better organization
            sources_by_language = {}
            for i, source in enumerate(sources, 1):
                language = source.get('language', 'unknown')
                if language not in sources_by_language:
                    sources_by_language[language] = []
                
                file_path = source.get('relative_path', 'Unknown')
                chunk_type = source.get('type', 'unknown')
                start_line = source.get('start_line', '?')
                end_line = source.get('end_line', '?')
                
                # Add identifier if available
                identifier = ""
                if chunk_type == 'function' and 'function_name' in source:
                    identifier = f" `{source['function_name']}()`"
                elif chunk_type == 'class' and 'class_name' in source:
                    identifier = f" `{source['class_name']}`"
                elif chunk_type == 'method':
                    class_name = source.get('class_name', 'Unknown')
                    method_name = source.get('method_name', 'Unknown')
                    identifier = f" `{class_name}::{method_name}()`"
                
                sources_by_language[language].append(
                    f"   {i}. `{file_path}` ({chunk_type}{identifier}, lines {start_line}-{end_line})"
                )
            
            result_parts.append(f"\n📚 **Based on {len(chunks)} code sections:**")
            for language, lang_sources in sources_by_language.items():
                result_parts.append(f"\n**{language.upper()} Code:**")
                result_parts.extend(lang_sources)
        
        result_text = "\n".join(result_parts)
        
        print(f"✅ [ENDPOINT] AI analysis complete", flush=True)
        return JSONResponse(content={"result": result_text})
        
    except Exception as e:
        print(f"❌ [ENDPOINT] Code question error: {e}", flush=True)
        logger.error(f"Code question error: {e}")
        return JSONResponse(content={"result": f"❌ Error asking about code: {str(e)}"})

@app.post("/tools/get_code_stats")
async def get_code_stats(args: CodebaseSelectArgs = Body(...)):
    """Get comprehensive statistics about a specific codebase"""
    print(f"📊 [ENDPOINT] Getting stats for: {args.codebase_name}", flush=True)
    
    if rag_service is None:
        raise HTTPException(status_code=503, detail="RAG service not available")
    
    try:
        stats = rag_service.get_codebase_stats(args.codebase_name)
        
        if 'error' in stats:
            return JSONResponse(content={"result": f"❌ Error getting stats: {stats['error']}"})
        
        # Format stats for display
        total_docs = stats.get('total_documents', 0)
        unique_files = stats.get('unique_files', 0)
        chunk_types = stats.get('chunk_types', {})
        languages = stats.get('languages', {})
        last_updated = stats.get('last_updated', 'unknown')
        
        result_parts = [
            f"📊 **Multi-Language Codebase Statistics: {args.codebase_name}**",
            f"📄 **Total code chunks**: {total_docs:,}",
            f"📁 **Unique files**: {unique_files:,}",
            f"🕒 **Last updated**: {last_updated}",
            "",
            "💻 **Programming Languages:**"
        ]
        
        # Sort languages by count
        sorted_languages = sorted(languages.items(), key=lambda x: x[1], reverse=True)
        for language, count in sorted_languages:
            percentage = (count / total_docs * 100) if total_docs > 0 else 0
            language_display = {
                'c': 'C',
                'cpp': 'C++', 
                'python': 'Python',
                'csharp': 'C#'
            }.get(language.lower(), language.upper())
            result_parts.append(f"   • **{language_display}**: {count:,} chunks ({percentage:.1f}%)")
        
        result_parts.extend([
            "",
            "🏗️ **Code Structure Types:**"
        ])
        
        # Sort chunk types by count
        sorted_types = sorted(chunk_types.items(), key=lambda x: x[1], reverse=True)
        for chunk_type, count in sorted_types:
            percentage = (count / total_docs * 100) if total_docs > 0 else 0
            type_display = {
                'function': 'Functions',
                'class': 'Classes',
                'method': 'Methods',
                'namespace': 'Namespaces',
                'struct_specifier': 'Structs',
                'enum_specifier': 'Enums',
                'import': 'Imports',
                'header': 'Headers/Imports'
            }.get(chunk_type, chunk_type.title())
            result_parts.append(f"   • **{type_display}**: {count:,} ({percentage:.1f}%)")
        
        result_text = "\n".join(result_parts)
        
        print(f"✅ [ENDPOINT] Stats retrieved for {args.codebase_name}", flush=True)
        return JSONResponse(content={"result": result_text})
        
    except Exception as e:
        print(f"❌ [ENDPOINT] Code stats error: {e}", flush=True)
        logger.error(f"Code stats error: {e}")
        return JSONResponse(content={"result": f"❌ Error getting code stats: {str(e)}"})

# --- Health Check and Info ---

@app.get("/")
async def read_root():
    print("🏠 [ENDPOINT] Root endpoint called", flush=True)
    
    ollama_status = "unknown"
    try:
        # Create client with proper host
        client = ollama.Client(host=OLLAMA_HOST)
        models = client.list()
        ollama_status = "connected"
    except Exception:
        ollama_status = "disconnected"
    
    # Get current codebase info
    current_info = None
    if current_codebase and rag_service:
        try:
            current_info = rag_service._get_codebase_info(current_codebase)
        except Exception:
            pass
    
    return {
        "message": "OpenWebUI Enhanced Multi-Language RAG Tool Server",
        "version": "2.1.0",
        "supported_languages": ["C", "C++", "Python", "C#"],
        "ollama_host": OLLAMA_HOST,
        "ollama_status": ollama_status,
        "current_codebase": current_codebase,
        "current_codebase_info": current_info,
        "rag_service_status": "available" if rag_service else "unavailable",
        "features": [
            "multi_language_support",
            "multi_codebase_support",
            "code_preprocessing", 
            "vector_database_creation",
            "intelligent_search",
            "ai_powered_analysis",
            "cross_language_analysis"
        ],
        "tools": [
            "list_codebases",
            "select_codebase",
            "process_codebase",
            "search_code",
            "ask_about_code",
            "get_code_stats"
        ]
    }

@app.get("/health")
async def health_check():
    """Comprehensive health check"""
    print("🔧 [ENDPOINT] Health check called", flush=True)
    
    health_status = {"rag_tool_server": "healthy", "supported_languages": ["C", "C++", "Python", "C#"]}
    
    # Check RAG service
    if rag_service is None:
        health_status["rag_service"] = "unavailable"
    else:
        try:
            codebases = rag_service.list_available_codebases()
            health_status["rag_service"] = "healthy"
            health_status["available_codebases"] = len(codebases)
            health_status["current_codebase"] = current_codebase
            
            # Language breakdown
            language_stats = {}
            for cb in codebases:
                for lang in cb.get('detected_languages', []):
                    language_stats[lang] = language_stats.get(lang, 0) + 1
            health_status["languages_detected"] = language_stats
            
        except Exception as e:
            health_status["rag_service"] = f"error: {str(e)}"
    
    # Check Ollama connection
    try:
        # Create client with proper host
        client = ollama.Client(host=OLLAMA_HOST)
        models = client.list()
        health_status["ollama"] = "healthy"
        health_status["available_models"] = len(models.get("models", []))
    except Exception as e:
        health_status["ollama"] = f"unavailable: {str(e)}"
    
    # Check source code directory
    source_path = Path(SOURCE_CODE_BASE_PATH)
    if source_path.exists():
        health_status["source_code_directory"] = "available"
        subdirs = [d for d in source_path.iterdir() if d.is_dir() and not d.name.startswith('.')]
        health_status["source_subdirectories"] = len(subdirs)
        
        # Check for supported file types
        supported_extensions = {'.c', '.cpp', '.cxx', '.cc', '.c++', '.h', '.hpp', '.hxx', '.hh', '.py', '.pyw', '.cs'}
        found_extensions = set()
        for subdir in subdirs:
            for file in subdir.rglob('*'):
                if file.suffix.lower() in supported_extensions:
                    found_extensions.add(file.suffix.lower())
        health_status["detected_file_types"] = list(found_extensions)
    else:
        health_status["source_code_directory"] = "missing"
    
    # Check ChromaDB directory
    chroma_path = Path(CHROMA_DB_BASE_PATH)
    if chroma_path.exists():
        health_status["chroma_db_directory"] = "available"
    else:
        health_status["chroma_db_directory"] = "missing"
    
    return health_status

# --- Legacy API Compatibility ---

@app.get("/stats")
async def get_stats():
    """Get collection statistics (compatible with original rag_service API)"""
    if not current_codebase:
        raise HTTPException(status_code=400, detail="No codebase selected")
    
    if rag_service is None:
        raise HTTPException(status_code=503, detail="RAG service not available")
    
    return rag_service.get_codebase_stats(current_codebase)

@app.post("/search")
async def search_endpoint(request: dict = Body(...)):
    """Direct search endpoint (compatible with original rag_service API)"""
    if not current_codebase:
        raise HTTPException(status_code=400, detail="No codebase selected")
    
    if rag_service is None:
        raise HTTPException(status_code=503, detail="RAG service not available")
    
    try:
        query = request.get("query", "")
        n_results = request.get("n_results", 5)
        filter_type = request.get("filter_type")
        filter_language = request.get("filter_language")
        filter_file = request.get("filter_file")
        
        results = rag_service.search(
            query=query,
            codebase_name=current_codebase,
            n_results=n_results,
            filter_type=filter_type,
            filter_language=filter_language,
            filter_file=filter_file
        )
        
        return {
            "results": results,
            "query": query,
            "codebase": current_codebase,
            "total_results": len(results),
            "supported_languages": ["C", "C++", "Python", "C#"]
        }
        
    except Exception as e:
        logger.error(f"Search endpoint error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/ask")
async def ask_endpoint(request: dict = Body(...)):
    """Direct ask endpoint (compatible with original rag_service API)"""
    if not current_codebase:
        raise HTTPException(status_code=400, detail="No codebase selected")
    
    if rag_service is None:
        raise HTTPException(status_code=503, detail="RAG service not available")
    
    try:
        query = request.get("query", "")
        n_results = request.get("n_results", 5)
        filter_type = request.get("filter_type")
        filter_language = request.get("filter_language")
        filter_file = request.get("filter_file")
        
        # Get relevant chunks
        chunks = rag_service.search(
            query=query,
            codebase_name=current_codebase,
            n_results=n_results,
            filter_type=filter_type,
            filter_language=filter_language,
            filter_file=filter_file
        )
        
        # Generate response
        response = rag_service.generate_response(query, chunks)
        
        return {
            "response": response,
            "sources": [chunk['metadata'] for chunk in chunks],
            "query": query,
            "codebase": current_codebase,
            "context_used": len(chunks),
            "supported_languages": ["C", "C++", "Python", "C#"]
        }
        
    except Exception as e:
        logger.error(f"Ask endpoint error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    print("🚀 [STARTUP] Starting uvicorn server with multi-language support...", flush=True)
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=5002, log_level="debug" if DEBUG else "info")