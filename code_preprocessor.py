# code_preprocessor.py
import os
from pathlib import Path
import hashlib
import json
from typing import Dict

class MultiLanguageCodeProcessor:
    def __init__(self, repo_path):
        self.repo_path = Path(repo_path)
        
        # Use tree-sitter-language-pack for all languages
        try:
            from tree_sitter_language_pack import get_language, get_parser
            
            # C/C++ languages
            self.c_language = get_language('c')
            self.c_parser = get_parser('c')
            self.cpp_language = get_language('cpp')
            self.cpp_parser = get_parser('cpp')
            
            # New languages
            self.python_language = get_language('python')
            self.python_parser = get_parser('python')
            self.csharp_language = get_language('c_sharp')
            self.csharp_parser = get_parser('c_sharp')
            
            print("Using tree-sitter-language-pack for C, C++, Python, and C#")
        except ImportError as e:
            raise RuntimeError(f"tree-sitter-language-pack is required. Please install it: pip install tree-sitter-language-pack. Error: {e}")
    
    def get_parser_for_file(self, filepath):
        """Get appropriate parser based on file extension"""
        suffix = filepath.suffix.lower()
        
        # C++ files
        if suffix in ['.cpp', '.cxx', '.cc', '.c++']:
            return self.cpp_parser, self.cpp_language, 'cpp'
        
        # C files
        elif suffix in ['.c', '.h']:
            return self.c_parser, self.c_language, 'c'
        
        # C++ headers
        elif suffix in ['.hpp', '.hxx', '.hh']:
            return self.cpp_parser, self.cpp_language, 'cpp'
        
        # Python files
        elif suffix in ['.py', '.pyw']:
            return self.python_parser, self.python_language, 'python'
        
        # C# files
        elif suffix in ['.cs']:
            return self.csharp_parser, self.csharp_language, 'csharp'
        
        else:
            # Default to C parser for unknown extensions
            return self.c_parser, self.c_language, 'c'
    
    def extract_functions(self, source_code, filepath):
        """Extract function definitions with their signatures and bodies"""
        parser, language, lang_name = self.get_parser_for_file(filepath)
        tree = parser.parse(bytes(source_code, "utf8"))
        functions = []
        
        def traverse(node, depth=0):
            # Handle different function types based on language
            if lang_name == 'python':
                if node.type == 'function_definition':
                    func_text = source_code[node.start_byte:node.end_byte]
                    func_name = self._get_python_function_name(node, source_code)
                    functions.append({
                        'name': func_name,
                        'content': func_text,
                        'start_line': node.start_point[0] + 1,
                        'end_line': node.end_point[0] + 1,
                        'filepath': str(filepath)
                    })
            
            elif lang_name == 'csharp':
                if node.type == 'method_declaration':
                    func_text = source_code[node.start_byte:node.end_byte]
                    func_name = self._get_csharp_method_name(node, source_code)
                    functions.append({
                        'name': func_name,
                        'content': func_text,
                        'start_line': node.start_point[0] + 1,
                        'end_line': node.end_point[0] + 1,
                        'filepath': str(filepath)
                    })
            
            else:  # C/C++
                if node.type == 'function_definition':
                    func_text = source_code[node.start_byte:node.end_byte]
                    func_name = self._get_function_name(node, source_code)
                    functions.append({
                        'name': func_name,
                        'content': func_text,
                        'start_line': node.start_point[0] + 1,
                        'end_line': node.end_point[0] + 1,
                        'filepath': str(filepath)
                    })
            
            for child in node.children:
                traverse(child, depth + 1)
        
        traverse(tree.root_node)
        return functions
    
    def extract_classes_and_methods(self, source_code, filepath):
        """Extract class definitions and methods for all languages"""
        parser, language, lang_name = self.get_parser_for_file(filepath)
        tree = parser.parse(bytes(source_code, "utf8"))
        classes = []
        
        def traverse(node):
            class_node_types = []
            
            if lang_name == 'python':
                class_node_types = ['class_definition']
            elif lang_name == 'csharp':
                class_node_types = ['class_declaration', 'interface_declaration', 'struct_declaration']
            else:  # C/C++
                class_node_types = ['class_specifier']
            
            if node.type in class_node_types:
                class_text = source_code[node.start_byte:node.end_byte]
                class_name = self._get_class_name_for_language(node, source_code, lang_name)
                
                # Extract methods within the class
                methods = []
                if lang_name == 'python':
                    methods = self._extract_python_methods_from_class(node, source_code)
                elif lang_name == 'csharp':
                    methods = self._extract_csharp_methods_from_class(node, source_code)
                else:  # C/C++
                    for child in node.children:
                        if child.type == 'field_declaration_list':
                            methods.extend(self._extract_methods_from_body(child, source_code))
                
                classes.append({
                    'name': class_name,
                    'content': class_text,
                    'methods': methods,
                    'start_line': node.start_point[0] + 1,
                    'end_line': node.end_point[0] + 1,
                    'filepath': str(filepath)
                })
            
            for child in node.children:
                traverse(child)
        
        traverse(tree.root_node)
        return classes
    
    def extract_structs_and_typedefs(self, source_code, filepath):
        """Extract struct definitions and typedefs"""
        parser, language, lang_name = self.get_parser_for_file(filepath)
        tree = parser.parse(bytes(source_code, "utf8"))
        definitions = []
        
        def traverse(node):
            definition_types = []
            
            if lang_name == 'python':
                # Python doesn't have structs/typedefs, but we can capture decorators, imports
                definition_types = ['import_statement', 'import_from_statement', 'decorated_definition']
            elif lang_name == 'csharp':
                definition_types = ['struct_declaration', 'enum_declaration', 'delegate_declaration', 'using_directive']
            else:  # C/C++
                definition_types = ['struct_specifier', 'typedef_declaration', 'enum_specifier', 'union_specifier']
            
            if node.type in definition_types:
                def_text = source_code[node.start_byte:node.end_byte]
                definitions.append({
                    'type': node.type,
                    'content': def_text,
                    'start_line': node.start_point[0] + 1,
                    'end_line': node.end_point[0] + 1,
                    'filepath': str(filepath)
                })
            
            for child in node.children:
                traverse(child)
        
        traverse(tree.root_node)
        return definitions
    
    def extract_namespaces(self, source_code, filepath):
        """Extract namespace definitions (C#/C++ only)"""
        parser, language, lang_name = self.get_parser_for_file(filepath)
        tree = parser.parse(bytes(source_code, "utf8"))
        namespaces = []
        
        # Python doesn't have namespaces, skip for Python files
        if lang_name == 'python':
            return namespaces
        
        def traverse(node):
            namespace_types = []
            
            if lang_name == 'csharp':
                namespace_types = ['namespace_declaration']
            else:  # C/C++
                namespace_types = ['namespace_definition']
            
            if node.type in namespace_types:
                ns_text = source_code[node.start_byte:node.end_byte]
                ns_name = self._get_namespace_name_for_language(node, source_code, lang_name)
                namespaces.append({
                    'name': ns_name,
                    'content': ns_text,
                    'start_line': node.start_point[0] + 1,
                    'end_line': node.end_point[0] + 1,
                    'filepath': str(filepath)
                })
            
            for child in node.children:
                traverse(child)
        
        traverse(tree.root_node)
        return namespaces
    
    def extract_template_definitions(self, source_code, filepath):
        """Extract template definitions (C++ only)"""
        parser, language, lang_name = self.get_parser_for_file(filepath)
        tree = parser.parse(bytes(source_code, "utf8"))
        templates = []
        
        # Only applicable to C++
        if lang_name != 'cpp':
            return templates
        
        def traverse(node):
            if node.type == 'template_declaration':
                template_text = source_code[node.start_byte:node.end_byte]
                templates.append({
                    'content': template_text,
                    'start_line': node.start_point[0] + 1,
                    'end_line': node.end_point[0] + 1,
                    'filepath': str(filepath)
                })
            
            for child in node.children:
                traverse(child)
        
        traverse(tree.root_node)
        return templates
    
    # Language-specific helper methods
    
    def _get_python_function_name(self, func_node, source_code):
        """Extract function name from Python function_definition node"""
        for child in func_node.children:
            if child.type == 'identifier':
                return source_code[child.start_byte:child.end_byte]
        return "unknown"
    
    def _get_csharp_method_name(self, method_node, source_code):
        """Extract method name from C# method_declaration node"""
        for child in method_node.children:
            if child.type == 'identifier':
                return source_code[child.start_byte:child.end_byte]
        return "unknown"
    
    def _get_function_name(self, func_node, source_code):
        """Extract function name from C/C++ function_definition node"""
        for child in func_node.children:
            if child.type == 'function_declarator':
                for grandchild in child.children:
                    if grandchild.type == 'identifier':
                        return source_code[grandchild.start_byte:grandchild.end_byte]
        return "unknown"
    
    def _get_class_name_for_language(self, class_node, source_code, lang_name):
        """Extract class name for different languages"""
        if lang_name == 'python':
            for child in class_node.children:
                if child.type == 'identifier':
                    return source_code[child.start_byte:child.end_byte]
        
        elif lang_name == 'csharp':
            for child in class_node.children:
                if child.type == 'identifier':
                    return source_code[child.start_byte:child.end_byte]
        
        else:  # C/C++
            for child in class_node.children:
                if child.type == 'type_identifier':
                    return source_code[child.start_byte:child.end_byte]
        
        return "unknown"
    
    def _get_namespace_name_for_language(self, ns_node, source_code, lang_name):
        """Extract namespace name for different languages"""
        for child in ns_node.children:
            if child.type == 'identifier':
                return source_code[child.start_byte:child.end_byte]
        return "anonymous"
    
    def _extract_python_methods_from_class(self, class_node, source_code):
        """Extract method definitions from Python class"""
        methods = []
        
        def traverse_class(node):
            if node.type == 'function_definition':
                method_text = source_code[node.start_byte:node.end_byte]
                method_name = self._get_python_function_name(node, source_code)
                methods.append({
                    'name': method_name,
                    'content': method_text,
                    'start_line': node.start_point[0] + 1,
                    'end_line': node.end_point[0] + 1
                })
            
            for child in node.children:
                traverse_class(child)
        
        traverse_class(class_node)
        return methods
    
    def _extract_csharp_methods_from_class(self, class_node, source_code):
        """Extract method definitions from C# class"""
        methods = []
        
        def traverse_class(node):
            if node.type == 'method_declaration':
                method_text = source_code[node.start_byte:node.end_byte]
                method_name = self._get_csharp_method_name(node, source_code)
                methods.append({
                    'name': method_name,
                    'content': method_text,
                    'start_line': node.start_point[0] + 1,
                    'end_line': node.end_point[0] + 1
                })
            
            for child in node.children:
                traverse_class(child)
        
        traverse_class(class_node)
        return methods
    
    def _extract_methods_from_body(self, body_node, source_code):
        """Extract method definitions from C++ class body"""
        methods = []
        
        def traverse_body(node):
            if node.type == 'function_definition':
                method_text = source_code[node.start_byte:node.end_byte]
                method_name = self._get_function_name(node, source_code)
                methods.append({
                    'name': method_name,
                    'content': method_text,
                    'start_line': node.start_point[0] + 1,
                    'end_line': node.end_point[0] + 1
                })
            
            for child in node.children:
                traverse_body(child)
        
        traverse_body(body_node)
        return methods
    
    def process_file(self, filepath):
        """Process a single source file"""
        try:
            with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
        except Exception as e:
            print(f"Error reading {filepath}: {e}")
            return []
        
        # Determine language
        parser, language, lang_name = self.get_parser_for_file(filepath)
        
        # Extract components based on language
        functions = self.extract_functions(content, filepath)
        definitions = self.extract_structs_and_typedefs(content, filepath)
        classes = self.extract_classes_and_methods(content, filepath)
        namespaces = self.extract_namespaces(content, filepath)
        
        # Templates only for C++
        templates = []
        if lang_name == 'cpp':
            templates = self.extract_template_definitions(content, filepath)
        
        # Create chunks
        chunks = []
        
        # Add file header chunk (imports, includes, defines, etc.)
        header_lines = []
        lines = content.split('\n')
        for i, line in enumerate(lines):
            stripped = line.strip()
            
            # Language-specific header detection
            if lang_name == 'python':
                if (stripped.startswith(('import ', 'from ', '#', 'encoding:', 'coding:')) or
                    stripped.startswith('"""') or stripped.startswith("'''")):
                    header_lines.append(f"{i+1}: {line}")
                elif stripped and not stripped.startswith(('class ', 'def ', 'if ', 'for ', 'while ')):
                    header_lines.append(f"{i+1}: {line}")
                elif stripped:
                    break
            
            elif lang_name == 'csharp':
                if (stripped.startswith(('using ', 'namespace ', '[', '//', '/*')) or
                    'assembly:' in stripped):
                    header_lines.append(f"{i+1}: {line}")
                elif stripped and not stripped.startswith(('class ', 'interface ', 'struct ', 'enum ')):
                    header_lines.append(f"{i+1}: {line}")
                elif stripped:
                    break
            
            else:  # C/C++
                if stripped.startswith(('#include', '#define', '#ifndef', '#ifdef', '#pragma', 'using namespace')):
                    header_lines.append(f"{i+1}: {line}")
                elif stripped and not stripped.startswith(('//','/*')):
                    break
        
        if header_lines:
            chunks.append({
                'content': '\n'.join(header_lines),
                'metadata': {
                    'type': 'header',
                    'filepath': str(filepath),
                    'relative_path': str(filepath.relative_to(self.repo_path)),
                    'language': lang_name,
                    'chunk_id': hashlib.md5(f"{filepath}_header".encode()).hexdigest()[:8]
                }
            })
        
        # Add function chunks
        for func in functions:
            chunks.append({
                'content': f"Function: {func['name']}\nFile: {func['filepath']}\nLines: {func['start_line']}-{func['end_line']}\n\n{func['content']}",
                'metadata': {
                    'type': 'function',
                    'function_name': func['name'],
                    'filepath': str(filepath),
                    'relative_path': str(filepath.relative_to(self.repo_path)),
                    'start_line': func['start_line'],
                    'end_line': func['end_line'],
                    'language': lang_name,
                    'chunk_id': hashlib.md5(f"{filepath}_{func['name']}".encode()).hexdigest()[:8]
                }
            })
        
        # Add class chunks
        for cls in classes:
            chunks.append({
                'content': f"Class: {cls['name']}\nFile: {cls['filepath']}\nLines: {cls['start_line']}-{cls['end_line']}\n\n{cls['content']}",
                'metadata': {
                    'type': 'class',
                    'class_name': cls['name'],
                    'filepath': str(filepath),
                    'relative_path': str(filepath.relative_to(self.repo_path)),
                    'start_line': cls['start_line'],
                    'end_line': cls['end_line'],
                    'language': lang_name,
                    'method_count': len(cls['methods']),
                    'chunk_id': hashlib.md5(f"{filepath}_{cls['name']}".encode()).hexdigest()[:8]
                }
            })
            
            # Add individual method chunks for large classes
            for method in cls['methods']:
                method_type = 'method' if lang_name in ['cpp', 'csharp'] else 'function'
                chunks.append({
                    'content': f"Method: {cls['name']}::{method['name']}\nFile: {cls['filepath']}\nLines: {method['start_line']}-{method['end_line']}\n\n{method['content']}",
                    'metadata': {
                        'type': method_type,
                        'class_name': cls['name'],
                        'method_name': method['name'],
                        'filepath': str(filepath),
                        'relative_path': str(filepath.relative_to(self.repo_path)),
                        'start_line': method['start_line'],
                        'end_line': method['end_line'],
                        'language': lang_name,
                        'chunk_id': hashlib.md5(f"{filepath}_{cls['name']}_{method['name']}".encode()).hexdigest()[:8]
                    }
                })
        
        # Add namespace chunks
        for ns in namespaces:
            chunks.append({
                'content': f"Namespace: {ns['name']}\nFile: {ns['filepath']}\nLines: {ns['start_line']}-{ns['end_line']}\n\n{ns['content']}",
                'metadata': {
                    'type': 'namespace',
                    'namespace_name': ns['name'],
                    'filepath': str(filepath),
                    'relative_path': str(filepath.relative_to(self.repo_path)),
                    'start_line': ns['start_line'],
                    'end_line': ns['end_line'],
                    'language': lang_name,
                    'chunk_id': hashlib.md5(f"{filepath}_{ns['name']}_ns".encode()).hexdigest()[:8]
                }
            })
        
        # Add template chunks (C++ only)
        for template in templates:
            chunks.append({
                'content': f"Template Definition\nFile: {template['filepath']}\nLines: {template['start_line']}-{template['end_line']}\n\n{template['content']}",
                'metadata': {
                    'type': 'template',
                    'filepath': str(filepath),
                    'relative_path': str(filepath.relative_to(self.repo_path)),
                    'start_line': template['start_line'],
                    'end_line': template['end_line'],
                    'language': lang_name,
                    'chunk_id': hashlib.md5(f"{filepath}_template_{template['start_line']}".encode()).hexdigest()[:8]
                }
            })
        
        # Add struct/typedef/enum chunks
        for definition in definitions:
            definition_type = definition['type']
            if lang_name == 'python':
                # Map Python-specific types
                type_map = {
                    'import_statement': 'import',
                    'import_from_statement': 'import',
                    'decorated_definition': 'decorator'
                }
                definition_type = type_map.get(definition_type, definition_type)
            
            chunks.append({
                'content': f"Definition ({definition_type})\nFile: {definition['filepath']}\nLines: {definition['start_line']}-{definition['end_line']}\n\n{definition['content']}",
                'metadata': {
                    'type': definition_type,
                    'filepath': str(filepath),
                    'relative_path': str(filepath.relative_to(self.repo_path)),
                    'start_line': definition['start_line'],
                    'end_line': definition['end_line'],
                    'language': lang_name,
                    'chunk_id': hashlib.md5(f"{filepath}_{definition['start_line']}".encode()).hexdigest()[:8]
                }
            })
        
        return chunks
    
    def process_repository(self, exclude_dirs=None):
        """Process entire repository recursively with multi-language support"""
        if exclude_dirs is None:
            exclude_dirs = {'.git', '.svn', 'build', 'dist', '__pycache__', 'node_modules', 
                           'bin', 'obj', '.vs', '.vscode', '.idea', 'packages'}
        
        all_chunks = []
        
        # Extended source extensions to include Python and C#
        source_extensions = {
            # C/C++
            '.c', '.cpp', '.cxx', '.cc', '.c++', '.h', '.hpp', '.hxx', '.hh',
            # Python
            '.py', '.pyw',
            # C#
            '.cs'
        }
        source_files = []
        
        def should_exclude_dir(dir_path):
            """Check if directory should be excluded"""
            return any(excluded in str(dir_path).lower() for excluded in exclude_dirs)
        
        print(f"Scanning {self.repo_path} recursively for source files...")
        print(f"Supported languages: C, C++, Python, C#")
        
        for root, dirs, files in os.walk(self.repo_path):
            root_path = Path(root)
            
            # Remove excluded directories from dirs list to prevent walking into them
            dirs[:] = [d for d in dirs if not should_exclude_dir(root_path / d)]
            
            for file in files:
                filepath = root_path / file
                if filepath.suffix.lower() in source_extensions:
                    source_files.append(filepath)
        
        # Sort files for consistent processing order
        source_files.sort()
        
        print(f"Found {len(source_files)} source files:")
        
        # Group by extension for summary
        by_ext = {}
        for f in source_files:
            ext = f.suffix.lower()
            by_ext[ext] = by_ext.get(ext, 0) + 1
        
        for ext, count in sorted(by_ext.items()):
            lang_name = self._get_language_name_from_extension(ext)
            print(f"  {ext} ({lang_name}): {count} files")
        
        # Process each file
        for filepath in source_files:
            rel_path = filepath.relative_to(self.repo_path)
            print(f"Processing: {rel_path}")
            try:
                chunks = self.process_file(filepath)
                all_chunks.extend(chunks)
                print(f"  -> Generated {len(chunks)} chunks")
            except Exception as e:
                print(f"  -> Error processing {rel_path}: {e}")
                continue
        
        print(f"\nProcessing complete:")
        print(f"  Total files processed: {len(source_files)}")
        print(f"  Total chunks generated: {len(all_chunks)}")
        
        # Show language breakdown
        language_chunks = {}
        for chunk in all_chunks:
            lang = chunk['metadata'].get('language', 'unknown')
            language_chunks[lang] = language_chunks.get(lang, 0) + 1
        
        print(f"  Chunks by language:")
        for lang, count in sorted(language_chunks.items()):
            print(f"    {lang}: {count} chunks")
        
        return all_chunks
    
    def _get_language_name_from_extension(self, ext):
        """Get human-readable language name from file extension"""
        ext_map = {
            '.c': 'C',
            '.h': 'C/C++',
            '.cpp': 'C++',
            '.cxx': 'C++',
            '.cc': 'C++',
            '.c++': 'C++',
            '.hpp': 'C++',
            '.hxx': 'C++',
            '.hh': 'C++',
            '.py': 'Python',
            '.pyw': 'Python',
            '.cs': 'C#'
        }
        return ext_map.get(ext.lower(), 'Unknown')


# Maintain backward compatibility
CppCodeProcessor = MultiLanguageCodeProcessor