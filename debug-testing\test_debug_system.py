#!/usr/bin/env python3
"""
Test script to help debug the codebase discovery system.
This script helps you test the RAG system with debug output enabled.
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

def set_debug_mode(enabled=True):
    """Set DEBUG environment variable"""
    if enabled:
        os.environ["DEBUG"] = "true"
        print("✅ Debug mode ENABLED")
    else:
        os.environ["DEBUG"] = "false"
        print("❌ Debug mode DISABLED")

def check_directory_structure():
    """Quick check of directory structure"""
    print("\n📁 Quick Directory Check:")
    
    source_path = Path("source_code")
    print(f"  source_code exists: {source_path.exists()}")
    
    if source_path.exists():
        subdirs = [d for d in source_path.iterdir() if d.is_dir() and not d.name.startswith('.')]
        print(f"  Found {len(subdirs)} subdirectories:")
        for subdir in subdirs:
            print(f"    - {subdir.name}")
    else:
        print("  ❌ source_code directory missing!")

def test_import():
    """Test importing the main module"""
    print("\n🧪 Testing Module Import:")
    try:
        # Add current directory to path
        sys.path.insert(0, str(Path.cwd()))
        
        # Try to import main components
        from main import MultiCodebaseRAGService, SOURCE_CODE_BASE_PATH, DEBUG
        print(f"✅ Successfully imported main components")
        print(f"  SOURCE_CODE_BASE_PATH: {SOURCE_CODE_BASE_PATH}")
        print(f"  DEBUG mode: {DEBUG}")
        
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_service_initialization():
    """Test RAG service initialization"""
    print("\n🚀 Testing Service Initialization:")
    try:
        from main import MultiCodebaseRAGService
        service = MultiCodebaseRAGService()
        print("✅ Service initialized successfully")
        
        # Test listing codebases
        print("\n📚 Testing Codebase Discovery:")
        codebases = service.list_available_codebases()
        print(f"Found {len(codebases)} codebases:")
        for cb in codebases:
            print(f"  - {cb['name']}: {cb['status']}")
        
        return True
    except Exception as e:
        print(f"❌ Service initialization failed: {e}")
        return False

def test_api_endpoint():
    """Test the API endpoint if server is running"""
    print("\n📡 Testing API Endpoint:")
    try:
        response = requests.post("http://localhost:5002/tools/list_codebases", timeout=5)
        if response.status_code == 200:
            print("✅ API endpoint responded successfully")
            result = response.json()
            print(f"Response: {result.get('result', 'No result field')[:200]}...")
        else:
            print(f"❌ API returned status {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API (server not running?)")
    except Exception as e:
        print(f"❌ API test failed: {e}")

def run_server_with_debug():
    """Run the server with debug enabled"""
    print("\n🖥️ Starting server with debug enabled...")
    print("Press Ctrl+C to stop the server")
    
    env = os.environ.copy()
    env["DEBUG"] = "true"
    
    try:
        subprocess.run([sys.executable, "main.py"], env=env)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")

def main():
    """Main test function"""
    print("🔧 RAG System Debug Test Tool")
    print("=" * 40)
    
    # Check directory structure first
    check_directory_structure()
    
    # Enable debug mode
    set_debug_mode(True)
    
    # Test import
    if not test_import():
        print("\n❌ Cannot proceed - import failed")
        return
    
    # Test service initialization
    if not test_service_initialization():
        print("\n❌ Service initialization failed")
    
    # Test API if available
    test_api_endpoint()
    
    print("\n" + "=" * 40)
    print("🎯 Test Summary:")
    print("1. If you see debug output above, the debug system is working")
    print("2. If no codebases were found, check your source_code directory")
    print("3. If API test failed, the server might not be running")
    
    print("\n💡 Next Steps:")
    print("1. Run: python debug_directory_structure.py")
    print("2. Create source_code/utils directory if needed")
    print("3. Add your C/C++ files to the directory")
    print("4. Start server: DEBUG=true python main.py")
    print("5. Or with Docker: DEBUG=true docker-compose up")
    
    # Ask if user wants to start server
    response = input("\n❓ Start server with debug enabled? (y/n): ").lower().strip()
    if response in ['y', 'yes']:
        run_server_with_debug()

if __name__ == "__main__":
    main()
