#!/usr/bin/env python3
"""
Test script to verify all requirements can be imported successfully.
This helps validate that the requirements.txt file is correct.
"""

import sys
import importlib

def test_import(module_name, package_name=None):
    """Test importing a module"""
    try:
        importlib.import_module(module_name)
        print(f"✓ {package_name or module_name}")
        return True
    except ImportError as e:
        print(f"✗ {package_name or module_name}: {e}")
        return False

def main():
    """Test all required packages"""
    print("Testing package imports from requirements.txt...")
    print("=" * 50)
    
    # Test packages from requirements.txt
    tests = [
        ("fastapi", "fastapi==0.104.1"),
        ("uvicorn", "uvicorn[standard]==0.24.0"),
        ("chromadb", "chromadb==0.4.15"),
        ("ollama", "ollama==0.1.7"),
        ("pydantic", "pydantic==2.5.0"),
        ("requests", "requests==2.31.0"),
        ("multipart", "python-multipart==0.0.6"),
        ("tree_sitter_language_pack", "tree-sitter-language-pack==0.8.0"),
    ]
    
    passed = 0
    total = len(tests)
    
    for module_name, package_name in tests:
        if test_import(module_name, package_name):
            passed += 1
    
    print("=" * 50)
    print(f"Import Results: {passed}/{total} packages imported successfully")
    
    if passed == total:
        print("🎉 All packages can be imported successfully!")
        return 0
    else:
        print("❌ Some packages failed to import.")
        print("You may need to install missing packages with:")
        print("pip install -r requirements.txt")
        return 1

if __name__ == "__main__":
    exit(main())
