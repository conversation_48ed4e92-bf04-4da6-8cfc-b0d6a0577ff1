# Complete Debugging Guide for Codebase Discovery

## Summary of Changes Made

### 1. Fixed Ollama Client Issues
- **Problem**: `ollama._client._host` attribute error
- **Solution**: Updated all ollama calls to use `ollama.Client(host=OLLAMA_HOST)`
- **Files**: `main.py` (4 locations fixed)

### 2. Added Comprehensive Debug System
- **Added DEBUG environment variable control**
- **Created `debug_print()` function** for conditional debug output
- **Added debug statements throughout the codebase discovery process**

### 3. Enhanced Docker Configuration
- **Added DEBUG environment variable support** in docker-compose.yml
- **Can now enable debug mode with `DEBUG=true docker-compose up`**

## How to Use the Debug System

### Method 1: Quick Test (Recommended)
```bash
# Run the comprehensive test script
python test_debug_system.py
```
This script will:
- Check your directory structure
- Test service initialization
- Show debug output
- Test API endpoints
- Optionally start the server with debug enabled

### Method 2: Manual Testing

#### Enable Debug Mode Locally:
```bash
# Set debug environment variable
export DEBUG=true  # Linux/Mac
set DEBUG=true     # Windows CMD
$env:DEBUG="true"  # Windows PowerShell

# Run the server
python main.py
```

#### Enable Debug Mode with Docker:
```bash
# Start with debug enabled
DEBUG=true docker-compose up --build
```

### Method 3: Directory Structure Check
```bash
# Check your directory setup first
python debug_directory_structure.py
```

## What the Debug Output Shows

### Service Initialization:
```
🚀 [DEBUG] Attempting to initialize RAG service...
🚀 [DEBUG] Initializing MultiCodebaseRAGService...
🚀 [DEBUG] CHROMA_DB_BASE_PATH: ./chroma_db
🚀 [DEBUG] SOURCE_CODE_BASE_PATH: ./source_code
🚀 [DEBUG] Current working directory: /your/project/path
✅ [DEBUG] RAG service initialized successfully!
```

### Codebase Discovery:
```
📡 [DEBUG] /tools/list_codebases endpoint called
📡 [DEBUG] RAG service is available, calling list_available_codebases()
🔍 [DEBUG] Starting codebase discovery...
🔍 [DEBUG] SOURCE_CODE_BASE_PATH: ./source_code
🔍 [DEBUG] Checking if source path exists: source_code
🔍 [DEBUG] Source path exists: True
🔍 [DEBUG] Source directory found, scanning contents...
🔍 [DEBUG] Found 1 items in source directory
🔍 [DEBUG] Examining item: utils (is_dir: True, starts_with_dot: False)
🔍 [DEBUG] Processing codebase directory: utils
```

### Individual Codebase Analysis:
```
🔍 [DEBUG] Getting info for codebase: utils
🔍 [DEBUG] Source path for utils: source_code/utils
🔍 [DEBUG] Source path exists: True
🔍 [DEBUG] Files in utils: 2 items
🔍 [DEBUG]   - test.c (file)
🔍 [DEBUG]   - test.h (file)
🔍 [DEBUG] Final status for utils: needs_indexing
```

## Common Issues and Solutions

### Issue 1: No Debug Output
**Cause**: DEBUG environment variable not set
**Solution**: 
```bash
export DEBUG=true
# or
DEBUG=true python main.py
```

### Issue 2: "Source code directory does not exist"
**Debug Output**:
```
❌ [DEBUG] Source code directory does not exist: source_code
```
**Solution**:
```bash
mkdir -p source_code/utils
# Copy your C/C++ files to source_code/utils/
```

### Issue 3: "Found 0 items in source directory"
**Debug Output**:
```
🔍 [DEBUG] Source directory found, scanning contents...
🔍 [DEBUG] Found 0 items in source directory
```
**Solution**: Add subdirectories with your codebases to the source_code directory

### Issue 4: "No C/C++ files found"
**Debug Output**:
```
🔍 [DEBUG] Files in utils: 3 items
🔍 [DEBUG]   - README.md (file)
🔍 [DEBUG]   - Makefile (file)
🔍 [DEBUG]   - docs (dir)
```
**Solution**: Ensure you have .c, .cpp, .h, .hpp files in the directory

## Testing the Fix

### Step 1: Check Directory Structure
```bash
python debug_directory_structure.py
```

### Step 2: Test with Debug Enabled
```bash
DEBUG=true python test_debug_system.py
```

### Step 3: Start Server and Test
```bash
# Start server with debug
DEBUG=true python main.py

# In another terminal, test the endpoint
curl -X POST http://localhost:5002/tools/list_codebases
```

### Step 4: Test with Docker
```bash
# Build and start with debug
DEBUG=true docker-compose up --build

# Test the endpoint
curl -X POST http://localhost:5002/tools/list_codebases
```

## Expected Successful Flow

1. **Service starts** with debug output showing paths
2. **Directory scanning** shows found subdirectories
3. **File analysis** shows C/C++ files in each codebase
4. **API response** returns list of available codebases
5. **Processing** works when you call process_codebase

## Disabling Debug Mode

Once you've identified and fixed the issue:

### Temporary Disable:
```bash
export DEBUG=false
# or just don't set DEBUG variable
```

### Permanent Disable:
Remove or comment out debug statements, or keep the DEBUG system for future troubleshooting.

## Files Created/Modified

### New Files:
1. `test_debug_system.py` - Comprehensive test script
2. `debug_directory_structure.py` - Directory structure checker
3. `DEBUGGING_COMPLETE_GUIDE.md` - This guide

### Modified Files:
1. `main.py` - Added debug system and fixed ollama client issues
2. `docker-compose.yml` - Added DEBUG environment variable support

## Next Steps

1. **Run the test script**: `python test_debug_system.py`
2. **Check directory structure**: Ensure source_code/utils exists with C/C++ files
3. **Start with debug**: `DEBUG=true python main.py` or `DEBUG=true docker-compose up`
4. **Test the endpoint**: Use curl or the OpenWebUI interface
5. **Process your codebase**: Once discovery works, process the utils codebase
6. **Disable debug**: Set `DEBUG=false` or remove debug statements when done

The debug system will help you pinpoint exactly where the codebase discovery is failing and guide you to the solution!
