# Setting Up Source Code Directory for RAG System

## Problem
The RAG system is looking for a `source_code/utils` directory that doesn't exist. The system expects a specific directory structure to process codebases.

## Required Directory Structure

The RAG system expects the following structure:

```
openwebui_rag__code_server/
├── source_code/                    # Main source code directory
│   ├── utils/                      # Your utils codebase
│   │   ├── *.c                     # C source files
│   │   ├── *.h                     # C header files
│   │   ├── *.cpp                   # C++ source files
│   │   ├── *.hpp                   # C++ header files
│   │   └── subdirectories/         # Any subdirectories with code
│   ├── project1/                   # Another codebase (optional)
│   ├── project2/                   # Another codebase (optional)
│   └── ...
├── chroma_db/                      # Vector database storage (auto-created)
├── main.py                         # RAG server
├── requirements.txt                # Dependencies
└── docker-compose.yml              # Docker configuration
```

## Solution Steps

### Step 1: Create the Source Code Directory Structure

Run these commands in your terminal:

```bash
# Create the main source_code directory
mkdir -p source_code

# Create the utils subdirectory
mkdir -p source_code/utils

# Create example subdirectories for other codebases (optional)
mkdir -p source_code/example_project
```

### Step 2: Add Your Code to the Utils Directory

You need to copy your actual C/C++ source code into the `source_code/utils/` directory. For example:

```bash
# Copy your utils code to the correct location
# Replace /path/to/your/utils/code with the actual path to your code
cp -r /path/to/your/utils/code/* source_code/utils/

# Or if you're on Windows:
# xcopy /E /I "C:\path\to\your\utils\code" "source_code\utils"
```

### Step 3: Verify the Directory Structure

After copying your code, verify the structure:

```bash
# List the contents to verify
ls -la source_code/
ls -la source_code/utils/

# On Windows:
# dir source_code
# dir source_code\utils
```

You should see your C/C++ files in the `source_code/utils/` directory.

### Step 4: Test the RAG System

Once the directory structure is correct, you can:

1. **List available codebases:**
   ```bash
   curl -X POST http://localhost:5002/tools/list_codebases
   ```

2. **Process the utils codebase:**
   ```bash
   curl -X POST http://localhost:5002/tools/process_codebase \
     -H "Content-Type: application/json" \
     -d '{"codebase_name": "utils", "exclude_dirs": ["build", "test"]}'
   ```

3. **Select the utils codebase:**
   ```bash
   curl -X POST http://localhost:5002/tools/select_codebase \
     -H "Content-Type: application/json" \
     -d '{"codebase_name": "utils"}'
   ```

## Supported File Types

The RAG system processes these file types:
- **C files:** `.c`, `.h`
- **C++ files:** `.cpp`, `.cxx`, `.cc`, `.c++`, `.hpp`, `.hxx`, `.hh`

## Common Issues and Solutions

### Issue 1: "Directory not found"
- **Cause:** The `source_code/utils` directory doesn't exist
- **Solution:** Create the directory structure as shown above

### Issue 2: "No code chunks generated"
- **Cause:** No supported files found in the directory
- **Solution:** Ensure you have C/C++ files in the directory

### Issue 3: "Permission denied"
- **Cause:** File system permissions
- **Solution:** Check file/directory permissions

### Issue 4: Empty directory
- **Cause:** The utils directory exists but is empty
- **Solution:** Copy your actual source code files to the directory

## Example: Creating a Test Utils Directory

If you don't have utils code ready, you can create a simple test:

```bash
# Create test files
mkdir -p source_code/utils

# Create a simple C file
cat > source_code/utils/test.c << 'EOF'
#include <stdio.h>
#include "test.h"

int add_numbers(int a, int b) {
    return a + b;
}

int main() {
    printf("Hello from utils!\n");
    return 0;
}
EOF

# Create a header file
cat > source_code/utils/test.h << 'EOF'
#ifndef TEST_H
#define TEST_H

int add_numbers(int a, int b);

#endif
EOF
```

## Next Steps

After setting up the directory structure:

1. **Start the RAG server:**
   ```bash
   docker-compose up --build
   ```

2. **Process your codebase:**
   Use the OpenWebUI interface or API calls to process the utils codebase

3. **Start analyzing:**
   Use the search and question tools to analyze your code

## Environment Variables

The system uses these paths (configurable via environment variables):

- `SOURCE_CODE_BASE_PATH`: Default is `./source_code`
- `CHROMA_DB_BASE_PATH`: Default is `./chroma_db`

You can override these in your docker-compose.yml or environment.
