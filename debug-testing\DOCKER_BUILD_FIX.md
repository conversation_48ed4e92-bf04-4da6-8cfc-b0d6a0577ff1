# Docker Build Fix for OpenWebUI RAG Server

## Problem
The Docker build was failing with the following error when trying to install `tree-sitter-language-pack==0.1.0`:

```
FileNotFoundError: [Errno 2] No such file or directory: '/tmp/pip-install-k78sgf63/tree-sitter-language-pack_633605c05bdb4921b3c4804377810668/parsers'
```

This error occurred because version 0.1.0 of the `tree-sitter-language-pack` package had build issues where it was looking for a `parsers` directory that didn't exist during the Docker build process.

## Solution
Updated the `requirements.txt` file to use the latest stable version of `tree-sitter-language-pack`:

**Before:**
```
tree-sitter-language-pack==0.1.0
```

**After:**
```
tree-sitter-language-pack==0.8.0
```

## Additional Changes Made

### 1. Fixed docker-compose.yml
- Updated build context from `./openwebui_rag_server` to `.` (current directory)
- Made the docker-compose.yml file standalone by adding proper version and services structure
- Removed dependency on ollama service and updated to use `host.docker.internal:11434`
- Cleaned up volumes section to only include necessary volumes

### 2. Created Test Scripts
- `test_tree_sitter.py`: Tests tree-sitter-language-pack functionality
- `test_requirements.py`: Tests all package imports from requirements.txt

## How to Test the Fix

### Option 1: Test Locally (Recommended)
1. Install the requirements locally:
   ```bash
   pip install -r requirements.txt
   ```

2. Run the tree-sitter test:
   ```bash
   python test_tree_sitter.py
   ```

3. Run the requirements test:
   ```bash
   python test_requirements.py
   ```

### Option 2: Test with Docker
1. Build the Docker image:
   ```bash
   docker-compose build
   ```

2. Run the container:
   ```bash
   docker-compose up
   ```

## Expected Results
- All package imports should succeed
- Tree-sitter should be able to load C and C++ parsers
- Basic parsing functionality should work
- Docker build should complete without errors

## Files Modified
1. `requirements.txt` - Updated tree-sitter-language-pack version
2. `docker-compose.yml` - Fixed build context and made standalone
3. `test_tree_sitter.py` - New test script (created)
4. `test_requirements.py` - New test script (created)
5. `DOCKER_BUILD_FIX.md` - This documentation (created)

## Background Information
The `tree-sitter-language-pack` package provides pre-built tree-sitter parsers for multiple programming languages. Version 0.1.0 had build issues, but version 0.8.0 (released June 2025) is stable and includes proper pre-built wheels that don't require compilation during installation.

This fix ensures that:
- The Docker build process completes successfully
- All required dependencies are properly installed
- The RAG server can parse C/C++ code as intended
- The service can run both in Docker and standalone environments

## Next Steps
After confirming the fix works:
1. Run the update script again: `./update-containers.sh`
2. Verify the openwebui-rag-server container starts successfully
3. Test the RAG functionality with actual C/C++ code
