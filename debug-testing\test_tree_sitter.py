#!/usr/bin/env python3
"""
Test script to verify tree-sitter-language-pack installation and functionality.
This script tests the import and basic functionality of the tree-sitter-language-pack
to ensure it works correctly with the updated version.
"""

def test_tree_sitter_import():
    """Test importing tree-sitter-language-pack"""
    try:
        from tree_sitter_language_pack import get_language, get_parser
        print("✓ Successfully imported tree_sitter_language_pack")
        return True
    except ImportError as e:
        print(f"✗ Failed to import tree_sitter_language_pack: {e}")
        return False

def test_c_language():
    """Test C language parser"""
    try:
        from tree_sitter_language_pack import get_language, get_parser
        c_language = get_language('c')
        c_parser = get_parser('c')
        print("✓ Successfully loaded C language and parser")
        return True
    except Exception as e:
        print(f"✗ Failed to load C language/parser: {e}")
        return False

def test_cpp_language():
    """Test C++ language parser"""
    try:
        from tree_sitter_language_pack import get_language, get_parser
        cpp_language = get_language('cpp')
        cpp_parser = get_parser('cpp')
        print("✓ Successfully loaded C++ language and parser")
        return True
    except Exception as e:
        print(f"✗ Failed to load C++ language/parser: {e}")
        return False

def test_parsing():
    """Test actual parsing functionality"""
    try:
        from tree_sitter_language_pack import get_parser
        
        # Test C code parsing
        c_parser = get_parser('c')
        c_code = """
        int main() {
            printf("Hello, World!");
            return 0;
        }
        """
        
        tree = c_parser.parse(bytes(c_code, "utf8"))
        if tree.root_node:
            print("✓ Successfully parsed C code")
            print(f"  Root node type: {tree.root_node.type}")
            print(f"  Number of children: {len(tree.root_node.children)}")
            return True
        else:
            print("✗ Failed to parse C code - no root node")
            return False
            
    except Exception as e:
        print(f"✗ Failed to parse C code: {e}")
        return False

def main():
    """Run all tests"""
    print("Testing tree-sitter-language-pack functionality...")
    print("=" * 50)
    
    tests = [
        test_tree_sitter_import,
        test_c_language,
        test_cpp_language,
        test_parsing
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! tree-sitter-language-pack is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    exit(main())
