import json
import sys
from typing import List, Dict, Optional

try:
    import chromadb
except ImportError as e:
    print(f"ChromaDB import error: {e}")
    sys.exit(1)

class VectorDBCreator:
    def __init__(self, db_path="./chroma_db"):
        """Initialize the vector database creator"""
        try:
            self.client = chromadb.PersistentClient(path=db_path)
            self.collection_name = "cpp_protocol_code"  # Default, will be updated
            print(f"Initialized ChromaDB client at {db_path}")
        except Exception as e:
            print(f"Error initializing ChromaDB client: {e}")
            raise
    
    def create_collection(self, chunks: List[Dict], collection_name: Optional[str] = None):
        """Create ChromaDB collection using default embedding function"""
        
        if collection_name:
            self.collection_name = collection_name
        
        # Delete existing collection if it exists
        try:
            self.client.delete_collection(self.collection_name)
            print(f"Deleted existing collection: {self.collection_name}")
        except Exception:
            print(f"No existing collection to delete: {self.collection_name}")
        
        # Create new collection - ChromaDB will use its default embedding function
        try:
            collection = self.client.create_collection(
                name=self.collection_name,
                metadata={"description": "C/C++ protocol stack source code with enhanced metadata"}
            )
            print(f"Created new collection: {self.collection_name}")
            print("Using ChromaDB's default embedding function")
        except Exception as e:
            print(f"Error creating collection: {e}")
            raise
        
        # Prepare data for insertion with enhanced content formatting
        documents = []
        metadatas = []
        ids = []
        
        for i, chunk in enumerate(chunks):
            # Enhanced document content with metadata context
            content = self._format_document_content(chunk)
            documents.append(content)
            
            # Ensure metadata is ChromaDB compatible (no nested objects)
            metadata = self._flatten_metadata(chunk['metadata'])
            metadatas.append(metadata)
            
            # Create more descriptive IDs
            chunk_id = chunk['metadata'].get('chunk_id', f'chunk_{i}')
            chunk_type = chunk['metadata'].get('type', 'unknown')
            ids.append(f"{chunk_type}_{i}_{chunk_id}")
        
        print(f"Adding {len(documents)} documents to collection...")
        print("ChromaDB will automatically generate embeddings")
        
        # Show statistics before insertion
        self._show_chunk_statistics(chunks)
        
        # Process in batches
        batch_size = 100
        total_batches = (len(documents) - 1) // batch_size + 1
        successful_batches = 0
        failed_chunks = []
        
        for i in range(0, len(documents), batch_size):
            batch_docs = documents[i:i+batch_size]
            batch_metas = metadatas[i:i+batch_size]
            batch_ids = ids[i:i+batch_size]
            
            batch_num = i // batch_size + 1
            print(f"Processing batch {batch_num}/{total_batches}")
            
            try:
                # ChromaDB automatically generates embeddings when you add documents
                collection.add(
                    documents=batch_docs,
                    metadatas=batch_metas,
                    ids=batch_ids
                )
                print(f"✓ Successfully added batch {batch_num}")
                successful_batches += 1
                
            except Exception as e:
                print(f"✗ Error processing batch {batch_num}: {e}")
                # Track failed chunks for debugging
                for j, chunk_id in enumerate(batch_ids):
                    failed_chunks.append({
                        'id': chunk_id,
                        'error': str(e),
                        'batch': batch_num
                    })
                continue
        
        print(f"Successfully created collection with {successful_batches}/{total_batches} batches")
        print(f"Total documents in collection: {collection.count()}")
        
        if failed_chunks:
            print(f"⚠ Warning: {len(failed_chunks)} chunks failed to insert")
            # Save failed chunks for debugging
            with open("failed_chunks.json", "w") as f:
                json.dump(failed_chunks, f, indent=2)
            print("Failed chunk details saved to failed_chunks.json")
        
        return collection
    
    def _format_document_content(self, chunk: Dict) -> str:
        """Format document content with metadata context for better embeddings"""
        metadata = chunk['metadata']
        content = chunk['content']
        
        # Add contextual information to help with semantic search
        context_parts = []
        
        # Add file context
        rel_path = metadata.get('relative_path', metadata.get('filepath', ''))
        if rel_path:
            context_parts.append(f"File: {rel_path}")
        
        # Add language context
        language = metadata.get('language', 'unknown')
        context_parts.append(f"Language: {language}")
        
        # Add type-specific context
        chunk_type = metadata.get('type', 'unknown')
        context_parts.append(f"Type: {chunk_type}")
        
        # Add name-specific context if available
        if chunk_type == 'function' and 'function_name' in metadata:
            context_parts.append(f"Function: {metadata['function_name']}")
        elif chunk_type == 'class' and 'class_name' in metadata:
            context_parts.append(f"Class: {metadata['class_name']}")
            if 'method_count' in metadata:
                context_parts.append(f"Methods: {metadata['method_count']}")
        elif chunk_type == 'method' and 'class_name' in metadata and 'method_name' in metadata:
            context_parts.append(f"Method: {metadata['class_name']}::{metadata['method_name']}")
        elif chunk_type == 'namespace' and 'namespace_name' in metadata:
            context_parts.append(f"Namespace: {metadata['namespace_name']}")
        
        # Combine context with content
        context_header = " | ".join(context_parts)
        formatted_content = f"{context_header}\n\n{content}"
        
        return formatted_content
    
    def _flatten_metadata(self, metadata: Dict) -> Dict:
        """Flatten metadata to ensure ChromaDB compatibility"""
        flattened = {}
        
        for key, value in metadata.items():
            if isinstance(value, (str, int, float, bool)):
                flattened[key] = value
            elif value is None:
                flattened[key] = ""
            else:
                # Convert complex types to strings
                flattened[key] = str(value)
        
        return flattened
    
    def _show_chunk_statistics(self, chunks: List[Dict]):
        """Display statistics about the chunks being processed"""
        print("\n=== Chunk Statistics ===")
        
        # Count by type
        type_counts: Dict[str, int] = {}
        language_counts: Dict[str, int] = {}
        file_counts: Dict[str, int] = {}
        
        for chunk in chunks:
            metadata = chunk['metadata']
            
            chunk_type = metadata.get('type', 'unknown')
            type_counts[chunk_type] = type_counts.get(chunk_type, 0) + 1
            
            language = metadata.get('language', 'unknown')
            language_counts[language] = language_counts.get(language, 0) + 1
            
            filepath = metadata.get('relative_path', metadata.get('filepath', 'unknown'))
            file_counts[filepath] = file_counts.get(filepath, 0) + 1
        
        print(f"Total chunks: {len(chunks)}")
        
        print("\nBy type:")
        for chunk_type, count in sorted(type_counts.items()):
            print(f"  {chunk_type}: {count}")
        
        print("\nBy language:")
        for language, count in sorted(language_counts.items()):
            print(f"  {language}: {count}")
        
        print(f"\nFiles processed: {len(file_counts)}")
        print("========================\n")
    

    def test_collection(self, collection, test_queries: Optional[List[str]] = None):
        """Test the collection with sample queries"""
        if test_queries is None:
            # Use actual function names and terms from the code
            test_queries = [
                "tmwcrypto_encryptData",
                "tmwchnl_initChannel", 
                "tmwappl_closeApplication",
                "tmwdiag_error",
                "TMWCHNL_STAT_EVENT_ENUM",
                "TMWChannelStruct",
                "InfoDest",
                "encrypt decrypt"
            ]
        
        print("\n=== Testing Collection ===")
        
        # First, let's check if we can get any results at all
        try:
            print("Checking collection status...")
            total_count = collection.count()
            print(f"Total documents in collection: {total_count}")
            
            # Try a very broad query first
            broad_results = collection.query(
                query_texts=["function"],
                n_results=2,
                include=['metadatas', 'distances', 'documents']
            )
            print(f"Broad 'function' query returned {len(broad_results.get('documents', [[]])[0])} results")
            
        except Exception as e:
            print(f"Error checking collection: {e}")
        
        for query in test_queries:
            try:
                results = collection.query(
                    query_texts=[query],
                    n_results=3,
                    include=['metadatas', 'distances', 'documents']
                )
                
                print(f"\nQuery: '{query}'")
                
                # Debug: print raw results structure
                if results:
                    print(f"  Raw results keys: {list(results.keys())}")
                    if 'documents' in results:
                        print(f"  Documents length: {len(results['documents'])}")
                        if len(results['documents']) > 0:
                            print(f"  First doc list length: {len(results['documents'][0])}")
                
                # Check if results exist and have content
                if (results and 'documents' in results and 
                    results['documents'] and len(results['documents']) > 0 and
                    results['documents'][0]):
                    
                    documents = results['documents'][0]
                    metadatas = results['metadatas'][0] if 'metadatas' in results and results['metadatas'] else []
                    distances = results['distances'][0] if 'distances' in results and results['distances'] else []
                    
                    if len(documents) > 0:
                        for i in range(len(documents)):
                            doc = documents[i] if i < len(documents) else ""
                            metadata = metadatas[i] if i < len(metadatas) else {}
                            distance = distances[i] if i < len(distances) else 0.0
                            
                            chunk_type = metadata.get('type', 'unknown')
                            filepath = metadata.get('relative_path', 'unknown')
                            function_name = metadata.get('function_name', '')
                            
                            if function_name and function_name != 'unknown':
                                print(f"  {i+1}. [{chunk_type}] {function_name} in {filepath} (distance: {distance:.3f})")
                            else:
                                print(f"  {i+1}. [{chunk_type}] {filepath} (distance: {distance:.3f})")
                            
                            # Show a snippet of the content
                            content_snippet = doc[:100].replace('\n', ' ').strip() if doc else "No content"
                            print(f"      {content_snippet}...")
                    else:
                        print("  Empty documents list")
                else:
                    print("  No results found or empty results")
                    
            except Exception as e:
                print(f"  Error testing query '{query}': {e}")
                import traceback
                print(f"  Full error: {traceback.format_exc()}")
        
        print("=========================\n")