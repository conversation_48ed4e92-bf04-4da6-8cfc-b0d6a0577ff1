# Debug Statements Added for Codebase Discovery

## Overview
I've added comprehensive debug statements to help diagnose the codebase discovery issue. These debug statements will show exactly what the system is doing when looking for codebases.

## Debug Statements Added

### 1. Service Initialization (`main.py`)
- **Location**: `MultiCodebaseRAGService.__init__()` and service initialization section
- **What it shows**:
  - Configuration paths (SOURCE_CODE_BASE_PATH, CHROMA_DB_BASE_PATH)
  - Current working directory
  - Success/failure of service initialization

### 2. Codebase Discovery (`list_available_codebases()`)
- **Location**: `MultiCodebaseRAGService.list_available_codebases()`
- **What it shows**:
  - Source code path being checked
  - Whether the source directory exists
  - Contents of the source directory
  - Each item found and whether it's processed
  - ChromaDB collections found
  - Final list of codebases

### 3. Individual Codebase Info (`_get_codebase_info()`)
- **Location**: `MultiCodebaseRAGService._get_codebase_info()`
- **What it shows**:
  - Path being checked for each codebase
  - Whether source directory exists
  - Files found in each codebase directory
  - ChromaDB collection status
  - Final status determination

### 4. API Endpoint (`/tools/list_codebases`)
- **Location**: `list_codebases()` endpoint
- **What it shows**:
  - When the endpoint is called
  - RAG service availability
  - Number of codebases found
  - Response building process

## How to Use the Debug Output

### 1. Run the Debug Script First
```bash
python debug_directory_structure.py
```
This will show you:
- Current directory structure
- Whether source_code directory exists
- What's inside the directories
- Option to create test structure

### 2. Start the RAG Server
```bash
# If using Docker
docker-compose up --build

# Or if running locally
python main.py
```

### 3. Check the Debug Output
Look for these debug messages in the console:

**Service Initialization:**
```
🚀 [DEBUG] Attempting to initialize RAG service...
🚀 [DEBUG] Initializing MultiCodebaseRAGService...
🚀 [DEBUG] CHROMA_DB_BASE_PATH: ./chroma_db
🚀 [DEBUG] SOURCE_CODE_BASE_PATH: ./source_code
🚀 [DEBUG] Current working directory: /path/to/your/project
✅ [DEBUG] RAG service initialized successfully!
```

**Codebase Discovery:**
```
📡 [DEBUG] /tools/list_codebases endpoint called
📡 [DEBUG] RAG service is available, calling list_available_codebases()
🔍 [DEBUG] Starting codebase discovery...
🔍 [DEBUG] SOURCE_CODE_BASE_PATH: ./source_code
🔍 [DEBUG] Checking if source path exists: source_code
🔍 [DEBUG] Source path exists: True/False
```

### 4. Test the List Codebases Endpoint
```bash
curl -X POST http://localhost:5002/tools/list_codebases
```

## Common Issues and What to Look For

### Issue 1: Source Directory Missing
**Debug Output:**
```
❌ [DEBUG] Source code directory does not exist: source_code
🔍 [DEBUG] Current working directory: /some/path
```
**Solution:** Create the source_code directory and add your codebases

### Issue 2: Empty Source Directory
**Debug Output:**
```
🔍 [DEBUG] Source directory found, scanning contents...
🔍 [DEBUG] Found 0 items in source directory
```
**Solution:** Add subdirectories with your C/C++ code

### Issue 3: No C/C++ Files
**Debug Output:**
```
🔍 [DEBUG] Processing codebase directory: utils
🔍 [DEBUG] Files in utils: 5 items
🔍 [DEBUG]   - file1.txt (file)
🔍 [DEBUG]   - README.md (file)
```
**Solution:** Ensure you have .c, .cpp, .h, .hpp files in the directory

### Issue 4: Service Not Initialized
**Debug Output:**
```
❌ [DEBUG] Failed to initialize RAG service: [error message]
❌ [DEBUG] RAG service is None!
```
**Solution:** Check the error message for specific issues

## Expected Successful Output

When everything is working correctly, you should see:

```
🚀 [DEBUG] Attempting to initialize RAG service...
🚀 [DEBUG] Initializing MultiCodebaseRAGService...
🚀 [DEBUG] SOURCE_CODE_BASE_PATH: ./source_code
🚀 [DEBUG] Current working directory: /your/project/path
✅ [DEBUG] RAG service initialized successfully!

📡 [DEBUG] /tools/list_codebases endpoint called
📡 [DEBUG] RAG service is available, calling list_available_codebases()
🔍 [DEBUG] Starting codebase discovery...
🔍 [DEBUG] SOURCE_CODE_BASE_PATH: ./source_code
🔍 [DEBUG] Source path exists: True
🔍 [DEBUG] Source directory found, scanning contents...
🔍 [DEBUG] Found 1 items in source directory
🔍 [DEBUG] Examining item: utils (is_dir: True, starts_with_dot: False)
🔍 [DEBUG] Processing codebase directory: utils
🔍 [DEBUG] Getting info for codebase: utils
🔍 [DEBUG] Files in utils: 2 items
🔍 [DEBUG]   - test.c (file)
🔍 [DEBUG]   - test.h (file)
🔍 [DEBUG] Final status for utils: needs_indexing
📡 [DEBUG] Got 1 codebases from service
📡 [DEBUG] Building response for 1 codebases
```

## Removing Debug Statements

Once you've identified and fixed the issue, you can remove the debug statements by:

1. Searching for lines containing `[DEBUG]`
2. Removing or commenting out those print statements
3. Or create a DEBUG flag to control them:

```python
DEBUG = os.getenv("DEBUG", "false").lower() == "true"

if DEBUG:
    print(f"🔍 [DEBUG] Debug message here")
```

## Files Modified

1. **main.py** - Added debug statements throughout the codebase discovery process
2. **debug_directory_structure.py** - New standalone debug script
3. **DEBUG_STATEMENTS_ADDED.md** - This documentation

## Next Steps

1. Run `python debug_directory_structure.py` to check your setup
2. Create the source_code/utils directory if needed
3. Add your C/C++ files to the utils directory
4. Start the RAG server and check the debug output
5. Test the list_codebases endpoint
6. Once working, optionally remove debug statements
