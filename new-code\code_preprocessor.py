# code_preprocessor.py
import os
from pathlib import Path
import hashlib
import json
from typing import Dict

class CppCodeProcessor:
    def __init__(self, repo_path):
        self.repo_path = Path(repo_path)
        
        # Use only tree-sitter-language-pack
        try:
            from tree_sitter_language_pack import get_language, get_parser
            self.c_language = get_language('c')
            self.c_parser = get_parser('c')
            self.cpp_language = get_language('cpp')
            self.cpp_parser = get_parser('cpp')
            print("Using tree-sitter-language-pack for C and C++")
        except ImportError as e:
            raise RuntimeError(f"tree-sitter-language-pack is required. Please install it: pip install tree-sitter-language-pack. Error: {e}")
        
    def get_parser_for_file(self, filepath):
        """Get appropriate parser based on file extension"""
        suffix = filepath.suffix.lower()
        if suffix in ['.cpp', '.cxx', '.cc', '.c++']:
            return self.cpp_parser, self.cpp_language
        else:  # .c, .h files default to C parser
            return self.c_parser, self.c_language
    
    def extract_functions(self, source_code, filepath):
        """Extract function definitions with their signatures and bodies"""
        parser, language = self.get_parser_for_file(filepath)
        tree = parser.parse(bytes(source_code, "utf8"))
        functions = []
        
        def traverse(node, depth=0):
            if node.type == 'function_definition':
                func_text = source_code[node.start_byte:node.end_byte]
                func_name = self._get_function_name(node, source_code)
                functions.append({
                    'name': func_name,
                    'content': func_text,
                    'start_line': node.start_point[0] + 1,
                    'end_line': node.end_point[0] + 1,
                    'filepath': str(filepath)
                })
            
            for child in node.children:
                traverse(child, depth + 1)
        
        traverse(tree.root_node)
        return functions
    
    def extract_classes_and_methods(self, source_code, filepath):
        """Extract C++ class definitions and methods"""
        parser, language = self.get_parser_for_file(filepath)
        tree = parser.parse(bytes(source_code, "utf8"))
        classes = []
        
        def traverse(node):
            if node.type == 'class_specifier':
                class_text = source_code[node.start_byte:node.end_byte]
                class_name = self._get_class_name(node, source_code)
                
                # Extract methods within the class
                methods = []
                for child in node.children:
                    if child.type == 'field_declaration_list':
                        methods.extend(self._extract_methods_from_body(child, source_code))
                
                classes.append({
                    'name': class_name,
                    'content': class_text,
                    'methods': methods,
                    'start_line': node.start_point[0] + 1,
                    'end_line': node.end_point[0] + 1,
                    'filepath': str(filepath)
                })
            
            for child in node.children:
                traverse(child)
        
        traverse(tree.root_node)
        return classes
    
    def _extract_methods_from_body(self, body_node, source_code):
        """Extract method definitions from class body"""
        methods = []
        
        def traverse_body(node):
            if node.type == 'function_definition':
                method_text = source_code[node.start_byte:node.end_byte]
                method_name = self._get_function_name(node, source_code)
                methods.append({
                    'name': method_name,
                    'content': method_text,
                    'start_line': node.start_point[0] + 1,
                    'end_line': node.end_point[0] + 1
                })
            
            for child in node.children:
                traverse_body(child)
        
        traverse_body(body_node)
        return methods
    
    def _get_function_name(self, func_node, source_code):
        """Extract function name from function_definition node"""
        for child in func_node.children:
            if child.type == 'function_declarator':
                for grandchild in child.children:
                    if grandchild.type == 'identifier':
                        return source_code[grandchild.start_byte:grandchild.end_byte]
        return "unknown"
    
    def _get_class_name(self, class_node, source_code):
        """Extract class name from class_specifier node"""
        for child in class_node.children:
            if child.type == 'type_identifier':
                return source_code[child.start_byte:child.end_byte]
        return "unknown"
    
    def extract_structs_and_typedefs(self, source_code, filepath):
        """Extract struct definitions and typedefs"""
        parser, language = self.get_parser_for_file(filepath)
        tree = parser.parse(bytes(source_code, "utf8"))
        definitions = []
        
        def traverse(node):
            if node.type in ['struct_specifier', 'typedef_declaration', 'enum_specifier', 'union_specifier']:
                def_text = source_code[node.start_byte:node.end_byte]
                definitions.append({
                    'type': node.type,
                    'content': def_text,
                    'start_line': node.start_point[0] + 1,
                    'end_line': node.end_point[0] + 1,
                    'filepath': str(filepath)
                })
            
            for child in node.children:
                traverse(child)
        
        traverse(tree.root_node)
        return definitions
    
    def extract_namespaces(self, source_code, filepath):
        """Extract C++ namespace definitions"""
        parser, language = self.get_parser_for_file(filepath)
        tree = parser.parse(bytes(source_code, "utf8"))
        namespaces = []
        
        def traverse(node):
            if node.type == 'namespace_definition':
                ns_text = source_code[node.start_byte:node.end_byte]
                ns_name = self._get_namespace_name(node, source_code)
                namespaces.append({
                    'name': ns_name,
                    'content': ns_text,
                    'start_line': node.start_point[0] + 1,
                    'end_line': node.end_point[0] + 1,
                    'filepath': str(filepath)
                })
            
            for child in node.children:
                traverse(child)
        
        traverse(tree.root_node)
        return namespaces
    
    def _get_namespace_name(self, ns_node, source_code):
        """Extract namespace name"""
        for child in ns_node.children:
            if child.type == 'identifier':
                return source_code[child.start_byte:child.end_byte]
        return "anonymous"
    
    def extract_template_definitions(self, source_code, filepath):
        """Extract C++ template definitions"""
        parser, language = self.get_parser_for_file(filepath)
        tree = parser.parse(bytes(source_code, "utf8"))
        templates = []
        
        def traverse(node):
            if node.type == 'template_declaration':
                template_text = source_code[node.start_byte:node.end_byte]
                templates.append({
                    'content': template_text,
                    'start_line': node.start_point[0] + 1,
                    'end_line': node.end_point[0] + 1,
                    'filepath': str(filepath)
                })
            
            for child in node.children:
                traverse(child)
        
        traverse(tree.root_node)
        return templates
    
    def process_file(self, filepath):
        """Process a single C/C++/H file"""
        try:
            with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
        except Exception as e:
            print(f"Error reading {filepath}: {e}")
            return []
        
        # Determine file type
        is_cpp = filepath.suffix.lower() in ['.cpp', '.cxx', '.cc', '.c++']
        
        # Extract components
        functions = self.extract_functions(content, filepath)
        definitions = self.extract_structs_and_typedefs(content, filepath)
        
        # C++ specific extractions
        classes = []
        namespaces = []
        templates = []
        if is_cpp or filepath.suffix.lower() == '.h':  # Headers might contain C++ code
            classes = self.extract_classes_and_methods(content, filepath)
            namespaces = self.extract_namespaces(content, filepath)
            templates = self.extract_template_definitions(content, filepath)
        
        # Create chunks
        chunks = []
        
        # Add file header chunk (includes, defines, etc.)
        header_lines = []
        lines = content.split('\n')
        for i, line in enumerate(lines):
            stripped = line.strip()
            if stripped.startswith(('#include', '#define', '#ifndef', '#ifdef', '#pragma', 'using namespace')):
                header_lines.append(f"{i+1}: {line}")
            elif stripped and not stripped.startswith(('//','/*')):
                break
        
        if header_lines:
            chunks.append({
                'content': '\n'.join(header_lines),
                'metadata': {
                    'type': 'header',
                    'filepath': str(filepath),
                    'relative_path': str(filepath.relative_to(self.repo_path)),
                    'language': 'cpp' if is_cpp else 'c',
                    'chunk_id': hashlib.md5(f"{filepath}_header".encode()).hexdigest()[:8]
                }
            })
        
        # Add function chunks
        for func in functions:
            chunks.append({
                'content': f"Function: {func['name']}\nFile: {func['filepath']}\nLines: {func['start_line']}-{func['end_line']}\n\n{func['content']}",
                'metadata': {
                    'type': 'function',
                    'function_name': func['name'],
                    'filepath': str(filepath),
                    'relative_path': str(filepath.relative_to(self.repo_path)),
                    'start_line': func['start_line'],
                    'end_line': func['end_line'],
                    'language': 'cpp' if is_cpp else 'c',
                    'chunk_id': hashlib.md5(f"{filepath}_{func['name']}".encode()).hexdigest()[:8]
                }
            })
        
        # Add class chunks (C++)
        for cls in classes:
            chunks.append({
                'content': f"Class: {cls['name']}\nFile: {cls['filepath']}\nLines: {cls['start_line']}-{cls['end_line']}\n\n{cls['content']}",
                'metadata': {
                    'type': 'class',
                    'class_name': cls['name'],
                    'filepath': str(filepath),
                    'relative_path': str(filepath.relative_to(self.repo_path)),
                    'start_line': cls['start_line'],
                    'end_line': cls['end_line'],
                    'language': 'cpp',
                    'method_count': len(cls['methods']),
                    'chunk_id': hashlib.md5(f"{filepath}_{cls['name']}".encode()).hexdigest()[:8]
                }
            })
            
            # Add individual method chunks for large classes
            for method in cls['methods']:
                chunks.append({
                    'content': f"Method: {cls['name']}::{method['name']}\nFile: {cls['filepath']}\nLines: {method['start_line']}-{method['end_line']}\n\n{method['content']}",
                    'metadata': {
                        'type': 'method',
                        'class_name': cls['name'],
                        'method_name': method['name'],
                        'filepath': str(filepath),
                        'relative_path': str(filepath.relative_to(self.repo_path)),
                        'start_line': method['start_line'],
                        'end_line': method['end_line'],
                        'language': 'cpp',
                        'chunk_id': hashlib.md5(f"{filepath}_{cls['name']}_{method['name']}".encode()).hexdigest()[:8]
                    }
                })
        
        # Add namespace chunks (C++)
        for ns in namespaces:
            chunks.append({
                'content': f"Namespace: {ns['name']}\nFile: {ns['filepath']}\nLines: {ns['start_line']}-{ns['end_line']}\n\n{ns['content']}",
                'metadata': {
                    'type': 'namespace',
                    'namespace_name': ns['name'],
                    'filepath': str(filepath),
                    'relative_path': str(filepath.relative_to(self.repo_path)),
                    'start_line': ns['start_line'],
                    'end_line': ns['end_line'],
                    'language': 'cpp',
                    'chunk_id': hashlib.md5(f"{filepath}_{ns['name']}_ns".encode()).hexdigest()[:8]
                }
            })
        
        # Add template chunks (C++)
        for template in templates:
            chunks.append({
                'content': f"Template Definition\nFile: {template['filepath']}\nLines: {template['start_line']}-{template['end_line']}\n\n{template['content']}",
                'metadata': {
                    'type': 'template',
                    'filepath': str(filepath),
                    'relative_path': str(filepath.relative_to(self.repo_path)),
                    'start_line': template['start_line'],
                    'end_line': template['end_line'],
                    'language': 'cpp',
                    'chunk_id': hashlib.md5(f"{filepath}_template_{template['start_line']}".encode()).hexdigest()[:8]
                }
            })
        
        # Add struct/typedef/enum chunks
        for definition in definitions:
            chunks.append({
                'content': f"Definition ({definition['type']})\nFile: {definition['filepath']}\nLines: {definition['start_line']}-{definition['end_line']}\n\n{definition['content']}",
                'metadata': {
                    'type': definition['type'],
                    'filepath': str(filepath),
                    'relative_path': str(filepath.relative_to(self.repo_path)),
                    'start_line': definition['start_line'],
                    'end_line': definition['end_line'],
                    'language': 'cpp' if is_cpp else 'c',
                    'chunk_id': hashlib.md5(f"{filepath}_{definition['start_line']}".encode()).hexdigest()[:8]
                }
            })
        
        return chunks
    
    def process_repository(self, exclude_dirs=None):
        """Process entire repository recursively"""
        if exclude_dirs is None:
            exclude_dirs = {'.git', '.svn', 'build', 'dist', '__pycache__', 'node_modules'}
        
        all_chunks = []
        
        # Recursively find all C/C++/H files
        source_extensions = {'.c', '.cpp', '.cxx', '.cc', '.c++', '.h', '.hpp', '.hxx', '.hh'}
        source_files = []
        
        def should_exclude_dir(dir_path):
            """Check if directory should be excluded"""
            return any(excluded in str(dir_path).lower() for excluded in exclude_dirs)
        
        print(f"Scanning {self.repo_path} recursively for source files...")
        
        for root, dirs, files in os.walk(self.repo_path):
            root_path = Path(root)
            
            # Remove excluded directories from dirs list to prevent walking into them
            dirs[:] = [d for d in dirs if not should_exclude_dir(root_path / d)]
            
            for file in files:
                filepath = root_path / file
                if filepath.suffix.lower() in source_extensions:
                    source_files.append(filepath)
        
        # Sort files for consistent processing order
        source_files.sort()
        
        print(f"Found {len(source_files)} source files:")
        
        # Group by extension for summary
        by_ext = {}
        for f in source_files:
            ext = f.suffix.lower()
            by_ext[ext] = by_ext.get(ext, 0) + 1
        
        for ext, count in sorted(by_ext.items()):
            print(f"  {ext}: {count} files")
        
        # Process each file
        for filepath in source_files:
            rel_path = filepath.relative_to(self.repo_path)
            print(f"Processing: {rel_path}")
            try:
                chunks = self.process_file(filepath)
                all_chunks.extend(chunks)
                print(f"  -> Generated {len(chunks)} chunks")
            except Exception as e:
                print(f"  -> Error processing {rel_path}: {e}")
                continue
        
        return all_chunks