#!/usr/bin/env python3
"""
Simple test script to verify debug output works in Docker containers.
This script tests the basic debug functionality.
"""

import os
import sys
import time

def test_debug_output():
    """Test debug output with different methods"""
    print("🧪 Testing Debug Output Methods", flush=True)
    print("=" * 40, flush=True)
    
    # Test 1: Basic print
    print("✅ Test 1: Basic print() works", flush=True)
    
    # Test 2: Print with flush
    print("✅ Test 2: print() with flush=True works", flush=True)
    
    # Test 3: sys.stdout.write
    sys.stdout.write("✅ Test 3: sys.stdout.write works\n")
    sys.stdout.flush()
    
    # Test 4: Check environment variables
    debug_env = os.getenv("DEBUG", "not_set")
    pythonunbuffered = os.getenv("PYTHONUNBUFFERED", "not_set")
    
    print(f"🔍 DEBUG environment variable: {debug_env}", flush=True)
    print(f"🔍 PYTHONUNBUFFERED environment variable: {pythonunbuffered}", flush=True)
    
    # Test 5: Check if we're in a container
    if os.path.exists("/.dockerenv"):
        print("🐳 Running inside Docker container", flush=True)
    else:
        print("💻 Running on host system", flush=True)
    
    # Test 6: Current working directory
    print(f"📁 Current working directory: {os.getcwd()}", flush=True)
    
    # Test 7: List current directory contents
    print("📋 Current directory contents:", flush=True)
    try:
        for item in os.listdir("."):
            print(f"  - {item}", flush=True)
    except Exception as e:
        print(f"❌ Error listing directory: {e}", flush=True)
    
    print("=" * 40, flush=True)
    print("🎯 If you can see this output, debug printing works!", flush=True)

def test_debug_function():
    """Test the debug function from main.py"""
    print("\n🧪 Testing Debug Function from main.py", flush=True)
    print("=" * 40, flush=True)
    
    try:
        # Set DEBUG environment variable
        os.environ["DEBUG"] = "true"
        
        # Import and test the debug function
        sys.path.insert(0, ".")
        from main import debug_print, DEBUG
        
        print(f"🔍 DEBUG variable value: {DEBUG}", flush=True)
        
        debug_print("✅ debug_print() function works!")
        debug_print("🎉 This message should appear if DEBUG=true")
        
        # Test with DEBUG=false
        os.environ["DEBUG"] = "false"
        # Need to reload the module to pick up new env var
        import importlib
        import main
        importlib.reload(main)
        
        main.debug_print("❌ This message should NOT appear if DEBUG=false")
        
        print("✅ Debug function test completed", flush=True)
        
    except Exception as e:
        print(f"❌ Error testing debug function: {e}", flush=True)

def main():
    """Main test function"""
    print("🔧 Docker Debug Output Test", flush=True)
    print("Starting debug output tests...", flush=True)
    
    test_debug_output()
    test_debug_function()
    
    print("\n🏁 Test completed!", flush=True)
    print("If you can see all the messages above, debug output is working correctly.", flush=True)

if __name__ == "__main__":
    main()
