#!/usr/bin/env python3
"""
Debug script to check directory structure and help diagnose codebase discovery issues.
Run this script to see what the RAG system sees when looking for codebases.
"""

import os
from pathlib import Path

def check_directory_structure():
    """Check the current directory structure and source code setup"""
    print("🔍 Directory Structure Debug Report")
    print("=" * 50)
    
    # Current working directory
    cwd = Path.cwd()
    print(f"Current working directory: {cwd}")
    print(f"Absolute path: {cwd.absolute()}")
    print()
    
    # Check environment variables
    print("📋 Environment Variables:")
    source_code_path = os.getenv("SOURCE_CODE_BASE_PATH", "./source_code")
    chroma_db_path = os.getenv("CHROMA_DB_BASE_PATH", "./chroma_db")
    print(f"  SOURCE_CODE_BASE_PATH: {source_code_path}")
    print(f"  CHROMA_DB_BASE_PATH: {chroma_db_path}")
    print()
    
    # Check source_code directory
    print("📁 Source Code Directory Check:")
    source_path = Path(source_code_path)
    print(f"  Path: {source_path}")
    print(f"  Absolute path: {source_path.absolute()}")
    print(f"  Exists: {source_path.exists()}")
    
    if source_path.exists():
        print(f"  Is directory: {source_path.is_dir()}")
        if source_path.is_dir():
            try:
                items = list(source_path.iterdir())
                print(f"  Contains {len(items)} items:")
                
                for item in items:
                    item_type = "📁 dir " if item.is_dir() else "📄 file"
                    print(f"    {item_type} {item.name}")
                    
                    # If it's a directory, check for C/C++ files
                    if item.is_dir():
                        try:
                            code_files = []
                            for ext in ['.c', '.cpp', '.cxx', '.cc', '.c++', '.h', '.hpp', '.hxx', '.hh']:
                                code_files.extend(list(item.glob(f'*{ext}')))
                                code_files.extend(list(item.glob(f'**/*{ext}')))
                            
                            if code_files:
                                print(f"      └─ Contains {len(code_files)} C/C++ files")
                                for cf in code_files[:5]:  # Show first 5
                                    print(f"         - {cf.name}")
                                if len(code_files) > 5:
                                    print(f"         ... and {len(code_files) - 5} more")
                            else:
                                print(f"      └─ No C/C++ files found")
                        except Exception as e:
                            print(f"      └─ Error checking files: {e}")
                            
            except Exception as e:
                print(f"  Error listing directory: {e}")
    else:
        print("  ❌ Directory does not exist!")
        print()
        print("  💡 To fix this, create the directory structure:")
        print(f"     mkdir -p {source_path}")
        print(f"     mkdir -p {source_path}/utils")
        print("     # Then copy your C/C++ code to the utils directory")
    
    print()
    
    # Check current directory contents
    print("📁 Current Directory Contents:")
    try:
        items = list(cwd.iterdir())
        print(f"  Contains {len(items)} items:")
        for item in items:
            item_type = "📁 dir " if item.is_dir() else "📄 file"
            print(f"    {item_type} {item.name}")
    except Exception as e:
        print(f"  Error listing current directory: {e}")
    
    print()
    
    # Check ChromaDB directory
    print("🗄️ ChromaDB Directory Check:")
    chroma_path = Path(chroma_db_path)
    print(f"  Path: {chroma_path}")
    print(f"  Absolute path: {chroma_path.absolute()}")
    print(f"  Exists: {chroma_path.exists()}")
    
    if chroma_path.exists():
        try:
            items = list(chroma_path.iterdir())
            print(f"  Contains {len(items)} items")
        except Exception as e:
            print(f"  Error listing ChromaDB directory: {e}")
    
    print()
    print("=" * 50)
    print("🎯 Summary:")
    
    if not source_path.exists():
        print("❌ Source code directory is missing")
        print("   Create it with: mkdir -p source_code/utils")
    elif not source_path.is_dir():
        print("❌ Source code path exists but is not a directory")
    else:
        try:
            subdirs = [item for item in source_path.iterdir() if item.is_dir() and not item.name.startswith('.')]
            if not subdirs:
                print("⚠️  Source code directory exists but is empty")
                print("   Add your codebases as subdirectories")
            else:
                print(f"✅ Found {len(subdirs)} potential codebase directories")
                for subdir in subdirs:
                    print(f"   - {subdir.name}")
        except Exception as e:
            print(f"❌ Error checking source directory: {e}")

def create_test_structure():
    """Create a test directory structure with sample files"""
    print("\n🛠️ Creating test directory structure...")
    
    source_path = Path("source_code")
    utils_path = source_path / "utils"
    
    try:
        # Create directories
        utils_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {utils_path}")
        
        # Create test C file
        test_c = utils_path / "test.c"
        test_c.write_text("""#include <stdio.h>
#include "test.h"

int add_numbers(int a, int b) {
    return a + b;
}

int main() {
    printf("Hello from utils!\\n");
    return 0;
}
""")
        print(f"✅ Created test file: {test_c}")
        
        # Create test header file
        test_h = utils_path / "test.h"
        test_h.write_text("""#ifndef TEST_H
#define TEST_H

int add_numbers(int a, int b);

#endif
""")
        print(f"✅ Created test file: {test_h}")
        
        print("🎉 Test structure created successfully!")
        print("   You can now test the RAG system with the 'utils' codebase")
        
    except Exception as e:
        print(f"❌ Error creating test structure: {e}")

if __name__ == "__main__":
    check_directory_structure()
    
    # Ask if user wants to create test structure
    response = input("\n❓ Would you like to create a test directory structure? (y/n): ").lower().strip()
    if response in ['y', 'yes']:
        create_test_structure()
        print("\n🔄 Re-checking directory structure after creation:")
        check_directory_structure()
