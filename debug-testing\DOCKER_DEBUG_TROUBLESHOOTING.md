# Docker Debug Output Troubleshooting Guide

## Problem
Debug prints are not showing in Docker container logs.

## Solutions Applied

### 1. **Fixed Python Output Buffering**
- **Added `PYTHONUNBUFFERED=1`** to Docker<PERSON>le and docker-compose.yml
- **Added `flush=True`** to all print statements
- **Added early startup debug message** to confirm DEBUG mode is working

### 2. **Enhanced Debug Function**
- **Created `debug_print()`** function with forced flushing
- **Added DEBUG environment variable** control
- **Added immediate startup debug output**

## How to Test the Fix

### Step 1: Test Debug Output Locally
```bash
# Test the debug output script
python test_docker_debug.py
```

### Step 2: Build and Test with Docker
```bash
# Build with debug enabled
DEBUG=true docker-compose build

# Run with debug enabled and watch logs
DEBUG=true docker-compose up
```

### Step 3: Check Container Logs
```bash
# In another terminal, check logs
docker logs openwebui-rag-server

# Or follow logs in real-time
docker logs -f openwebui-rag-server
```

## Expected Debug Output

When working correctly, you should see:

```
🔥 [DEBUG] DEBUG MODE ENABLED - You should see debug output!
🔥 [DEBUG] Python version: 3.10.x
🔥 [DEBUG] Current working directory: /app
🚀 [DEBUG] Attempting to initialize RAG service...
🚀 [DEBUG] Initializing MultiCodebaseRAGService...
🚀 [DEBUG] CHROMA_DB_BASE_PATH: ./chroma_db
🚀 [DEBUG] SOURCE_CODE_BASE_PATH: ./source_code
🚀 [DEBUG] Current working directory: /app
✅ [DEBUG] RAG service initialized successfully!
```

## Troubleshooting Steps

### Issue 1: Still No Debug Output

**Check 1: Verify DEBUG Environment Variable**
```bash
# Check if DEBUG is set in container
docker exec openwebui-rag-server env | grep DEBUG
```

**Check 2: Verify PYTHONUNBUFFERED**
```bash
# Check if PYTHONUNBUFFERED is set
docker exec openwebui-rag-server env | grep PYTHONUNBUFFERED
```

**Check 3: Test Basic Output**
```bash
# Test basic output in container
docker exec openwebui-rag-server python -c "print('Test output', flush=True)"
```

### Issue 2: Container Not Starting

**Check Container Status:**
```bash
docker ps -a
```

**Check Build Logs:**
```bash
docker-compose build --no-cache
```

**Check Startup Logs:**
```bash
docker-compose logs openwebui-rag-server
```

### Issue 3: Environment Variables Not Working

**Method 1: Set in docker-compose.yml**
```yaml
environment:
  - DEBUG=true
  - PYTHONUNBUFFERED=1
```

**Method 2: Set via command line**
```bash
DEBUG=true PYTHONUNBUFFERED=1 docker-compose up
```

**Method 3: Set in .env file**
```bash
# Create .env file
echo "DEBUG=true" > .env
echo "PYTHONUNBUFFERED=1" >> .env
docker-compose up
```

## Alternative Debug Methods

### Method 1: Use Docker Logs with Timestamps
```bash
docker-compose logs -f -t openwebui-rag-server
```

### Method 2: Execute Commands in Running Container
```bash
# Get a shell in the container
docker exec -it openwebui-rag-server /bin/bash

# Run debug script inside container
python test_docker_debug.py
```

### Method 3: Use Volume Mount for Debug Files
Add to docker-compose.yml:
```yaml
volumes:
  - ./debug_output:/app/debug_output
```

Then modify debug_print to also write to file:
```python
def debug_print(message: str):
    if DEBUG:
        print(message, flush=True)
        # Also write to file
        with open("/app/debug_output/debug.log", "a") as f:
            f.write(f"{message}\n")
            f.flush()
```

## Quick Test Commands

### Test 1: Basic Container Function
```bash
# Start container and test basic endpoint
DEBUG=true docker-compose up -d
curl -X POST http://localhost:5002/tools/list_codebases
```

### Test 2: Check Debug in Real-time
```bash
# Terminal 1: Start with debug
DEBUG=true docker-compose up

# Terminal 2: Test endpoint
curl -X POST http://localhost:5002/tools/list_codebases
```

### Test 3: Interactive Debug Session
```bash
# Start container
DEBUG=true docker-compose up -d

# Get shell and test
docker exec -it openwebui-rag-server /bin/bash
python test_docker_debug.py
```

## Files Modified for Debug Fix

1. **Dockerfile** - Added `PYTHONUNBUFFERED=1`
2. **docker-compose.yml** - Added `PYTHONUNBUFFERED=1` and `DEBUG` support
3. **main.py** - Enhanced debug system with forced flushing
4. **test_docker_debug.py** - New test script for Docker debug output

## Verification Checklist

- [ ] `DEBUG=true` environment variable is set
- [ ] `PYTHONUNBUFFERED=1` is set in container
- [ ] Container starts without errors
- [ ] Early debug message appears in logs
- [ ] Service initialization debug messages appear
- [ ] API endpoint debug messages appear when called
- [ ] Debug output appears immediately (not buffered)

## If Debug Still Doesn't Work

1. **Check Docker version** - Some older versions have logging issues
2. **Try different log drivers** - Add to docker-compose.yml:
   ```yaml
   logging:
     driver: "json-file"
     options:
       max-size: "10m"
       max-file: "3"
   ```
3. **Use alternative output methods** - Write to stderr instead:
   ```python
   import sys
   print(message, file=sys.stderr, flush=True)
   ```

The key fixes are `PYTHONUNBUFFERED=1` and `flush=True` - these should resolve most Docker logging issues!
